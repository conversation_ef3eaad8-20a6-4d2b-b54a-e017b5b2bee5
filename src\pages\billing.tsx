import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useSession, signOut } from 'next-auth/react';
import { CreditCard, Download, Calendar, CheckCircle, X, LogOut } from 'lucide-react';
import { toast } from 'sonner';

interface BillingHistory {
  id: string;
  amount: number;
  status: string;
  date: string;
  description: string;
  invoice_url?: string;
}

export default function Billing() {
  const navigate = useNavigate();
  const { data: session, status } = useSession();

  const [subscription, setSubscription] = useState<any>(null);
  const [credits, setCredits] = useState<any>(null);
  const [plans, setPlans] = useState<any[]>([]);
  const [billingHistory, setBillingHistory] = useState<BillingHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [isCanceling, setIsCanceling] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user) {
      navigate('/signin');
      return;
    }
    fetchSubscription();
    fetchPlans();
    fetchBillingHistory();
  }, [session, status, navigate]);

  const fetchSubscription = async () => {
    try {
      const response = await fetch('/api/billing/subscription');
      if (response.ok) {
        const data = await response.json();
        setSubscription(data.data.subscription);
        setCredits(data.data.credits);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    }
  };

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/billing/plans');
      if (response.ok) {
        const data = await response.json();
        setPlans(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
    }
  };

  const fetchBillingHistory = async () => {
    try {
      const response = await fetch('/api/billing/history');
      if (response.ok) {
        const data = await response.json();
        setBillingHistory(data.history || []);
      }
    } catch (error) {
      console.error('Error fetching billing history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = async (planId: string = 'pro') => {
    if (!session?.user) return;
    
    setIsUpgrading(true);
    try {
      const response = await fetch('/api/billing/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: planId,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.url) {
          window.location.href = data.data.url;
        } else {
          toast.error('Failed to create checkout session');
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create checkout session');
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to upgrade subscription');
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription || !session?.user) return;
    
    setIsCanceling(true);
    try {
      const response = await fetch('/api/billing/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        toast.success('Subscription canceled successfully');
        fetchSubscription();
      } else {
        toast.error('Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setIsCanceling(false);
    }
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    navigate('/');
  };

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const currentPlan = subscription?.plan || 'free';
  const remainingCredits = credits?.remaining || 0;
  const totalCredits = credits?.total || 0;
  const isUnlimited = totalCredits === -1;
  
  // Get plan details from fetched plans
  const freePlan = plans.find(p => p.id === 'free');
  const proPlan = plans.find(p => p.id === 'pro');
  const enterprisePlan = plans.find(p => p.id === 'enterprise');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="brand">
                <span className="brand-logo">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                    <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                    <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
                  </svg>
                </span>
                <span className="brand-text">GenerateVBA</span>
              </Link>
            </div>
            <nav className="flex items-center space-x-4">
              <Link to="/dashboard" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                Dashboard
              </Link>
              <Link to="/generate" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                Generate
              </Link>
              <Link to="/history" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                History
              </Link>
              <Link to="/profile" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium transition-colors">
                Profile
              </Link>
              <div className="flex items-center space-x-3 ml-4 pl-4 border-l border-gray-200">
                <span className="text-sm text-gray-600">Hello, {session?.user?.name || 'User'}</span>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  <LogOut className="h-4 w-4 mr-1" />
                  Logout
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Billing & Subscription</h1>
            <p className="text-gray-600">Manage your subscription and billing information.</p>
          </div>

          {/* Current Plan */}
          <div className="card mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-6 w-6 text-purple-600" />
                <h2 className="text-xl font-bold">Current Plan</h2>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                currentPlan === "free" 
                  ? "bg-gray-100 text-gray-800" 
                  : currentPlan === "pro"
                  ? "bg-green-100 text-green-800"
                  : "bg-purple-100 text-purple-800"
              }`}>
                {currentPlan === "free" ? "Free Plan" : 
                 currentPlan === "pro" ? "Pro Plan" : "Enterprise Plan"}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {isUnlimited ? "∞" : totalCredits}
                </div>
                <div className="text-gray-600 text-sm">
                  {currentPlan === 'free' ? 'Total credits' : 'Credits per month'}
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold mb-1 ${
                  remainingCredits === 0 ? 'text-red-600' : 
                  remainingCredits <= 2 && !isUnlimited ? 'text-yellow-600' : 'text-purple-600'
                }`}>
                  {isUnlimited ? "∞" : remainingCredits}
                </div>
                <div className="text-gray-600 text-sm">Credits remaining</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  ${currentPlan === 'pro' && proPlan ? proPlan.price.toFixed(2) : "0.00"}
                </div>
                <div className="text-gray-600 text-sm">
                  {currentPlan === 'pro' ? 'Monthly cost' : 'Current cost'}
                </div>
              </div>
            </div>
            
            {subscription?.status === 'active' && subscription?.cancel_at_period_end && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <div className="text-yellow-500 mr-2">⚠️</div>
                  <div>
                    <h3 className="font-semibold text-yellow-900">Subscription Ending</h3>
                    <p className="text-yellow-700 text-sm">
                      Your subscription will end on {formatDate(new Date(subscription.current_period_end * 1000))}
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {currentPlan === "free" && (
              <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-purple-900">Upgrade to Pro</h3>
                    <p className="text-purple-700 text-sm">Get unlimited generations and advanced features</p>
                  </div>
                  <button 
                    onClick={() => handleUpgrade('pro')}
                    disabled={isUpgrading}
                    className="cta solid disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpgrading ? 'Processing...' : 'Upgrade Now'}
                  </button>
                </div>
              </div>
            )}
            
            {currentPlan === "pro" && !subscription?.cancel_at_period_end && (
              <div className="mt-6 p-4 bg-red-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-red-900">Cancel Subscription</h3>
                    <p className="text-red-700 text-sm">You can cancel anytime. Access continues until the end of your billing period.</p>
                  </div>
                  <button 
                    onClick={handleCancelSubscription}
                    disabled={isCanceling}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isCanceling ? 'Canceling...' : 'Cancel Plan'}
                  </button>
                </div>
              </div>
            )}
            
            {currentPlan === "enterprise" && (
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <div className="text-center">
                  <h3 className="font-semibold text-gray-900">Enterprise Plan</h3>
                  <p className="text-gray-700 text-sm">Your enterprise plan is managed by your administrator</p>
                </div>
              </div>
            )}
          </div>

          {/* Pricing Plans */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-center mb-8">Choose Your Plan</h2>
            


            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Free Plan */}
              {freePlan && (
                <div className={`card text-center ${currentPlan === "free" ? "border-2 border-purple-500" : ""}`}>
                  {currentPlan === "free" && (
                    <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold mb-4 inline-block">
                      Current Plan
                    </div>
                  )}
                  <h3 className="text-2xl font-bold mb-2">{freePlan.name}</h3>
                  <div className="text-4xl font-bold text-purple-600 mb-4">${freePlan.price.toFixed(2)}</div>
                  <ul className="text-left space-y-2 mb-6">
                    {freePlan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="text-green-500 mr-2">✅</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  {currentPlan !== "free" ? (
                    <button className="cta ghost w-full">Downgrade to Free</button>
                  ) : (
                    <div className="text-gray-500 text-sm">Your current plan</div>
                  )}
                </div>
              )}
              
              {/* Pro Plan */}
              {proPlan && (
                <div className={`card text-center ${currentPlan === "pro" ? "border-2 border-purple-500" : ""}`}>
                  {currentPlan === "pro" && (
                    <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold mb-4 inline-block">
                      Current Plan
                    </div>
                  )}
                  <h3 className="text-2xl font-bold mb-2">{proPlan.name}</h3>
                  <div className="text-4xl font-bold text-purple-600 mb-4">
                    ${proPlan.price.toFixed(2)}
                    <span className="text-lg">/month</span>
                  </div>
                  <ul className="text-left space-y-2 mb-6">
                    {proPlan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <span className="text-green-500 mr-2">✅</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  {currentPlan !== "pro" ? (
                    <button 
                      onClick={() => handleUpgrade('pro')}
                      disabled={isUpgrading}
                      className="cta solid w-full disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isUpgrading ? 'Processing...' : 'Upgrade to Pro'}
                    </button>
                  ) : (
                    <div className="flex items-center justify-center text-green-600 text-sm font-medium">
                      <CheckCircle className="h-4 w-4 mr-1" />
                      Your current plan
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Billing History */}
          <div className="card">
            <div className="flex items-center space-x-3 mb-6">
              <Calendar className="h-6 w-6 text-purple-600" />
              <h2 className="text-xl font-bold">Billing History</h2>
            </div>
            
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                <span className="ml-2 text-gray-600">Loading billing history...</span>
              </div>
            ) : billingHistory.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No billing history</h3>
                <p className="text-gray-500">
                  {currentPlan === "free" 
                    ? "You're currently on the free plan. Upgrade to Premium to see billing history."
                    : "No billing history available yet."}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {billingHistory.map((item) => (
                  <div key={item.id} className="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                    <div>
                      <div className="font-medium">{item.description}</div>
                      <div className="text-sm text-gray-500">Paid on {formatDate(item.date)}</div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="font-medium">${(item.amount / 100).toFixed(2)}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.status === 'paid' 
                          ? 'bg-green-100 text-green-800'
                          : item.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </span>
                      {item.invoice_url && (
                        <a
                          href={item.invoice_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-purple-600 hover:text-purple-500 text-sm transition-colors"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}