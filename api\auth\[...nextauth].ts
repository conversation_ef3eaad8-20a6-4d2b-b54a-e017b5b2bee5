import { NextAuthOptions } from 'next-auth'
import { MongoDBAdapter } from '@next-auth/mongodb-adapter'
import { MongoClient } from 'mongodb'
import bcrypt from 'bcryptjs'
import User from '../models/User'
import Database from '../config/database'
import { createRequire } from 'module'

// Use createRequire for CommonJS compatibility
const require = createRequire(import.meta.url)
const NextAuth = require('next-auth').default
const GoogleProvider = require('next-auth/providers/google').default
const CredentialsProvider = require('next-auth/providers/credentials').default

const client = new MongoClient(process.env.MONGODB_URI!)
const clientPromise = client.connect()

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          await Database.getInstance().connect()
          const user = await User.findOne({ email: credentials.email.toLowerCase() })
          
          if (!user || user.provider !== 'email') {
            return null
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password!)
          
          if (!isPasswordValid) {
            return null
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            image: user.image,
            provider: user.provider,
            subscription: user.subscription,
            credits: user.credits,
            preferences: user.preferences
          } as any
        } catch (error) {
          console.error('Auth error:', error)
          return null
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          await Database.getInstance().connect()
          
          // Check if user exists with this Google ID
          let existingUser = await User.findOne({ 
            providerId: account.providerAccountId,
            provider: 'google'
          })
          
          if (!existingUser) {
            // Check if user exists with same email but different provider
            const emailUser = await User.findOne({ email: user.email })
            
            if (emailUser) {
              // Link Google account to existing email user
              existingUser = await User.findByIdAndUpdate(
                emailUser._id,
                {
                  provider: 'google',
                  providerId: account.providerAccountId,
                  image: user.image
                },
                { new: true }
              )
            } else {
              // Create new user
              existingUser = await User.create({
                email: user.email,
                name: user.name,
                image: user.image,
                provider: 'google',
                providerId: account.providerAccountId,
                subscription: {
                  plan: 'free',
                  status: 'active'
                },
                credits: {
                  total: 5,
                  used: 0,
                  remaining: 5
                },
                preferences: {
                  theme: 'light',
                  language: 'en',
                  notifications: true
                }
              })
            }
          }
          
          // Update user object with database fields
          ;(user as any).id = existingUser._id.toString()
          ;(user as any).provider = 'google'
          ;(user as any).subscription = existingUser.subscription
          ;(user as any).credits = existingUser.credits
          ;(user as any).preferences = existingUser.preferences
          
          return true
        } catch (error) {
          console.error('Google sign in error:', error)
          return false
        }
      }
      
      return true
    },
    async jwt({ token, user, account }) {
      // Initial sign in
      if (user) {
        token.id = (user as any).id
        token.provider = (user as any).provider
        token.subscription = (user as any).subscription
        token.credits = (user as any).credits
        token.preferences = (user as any).preferences
      }
      
      // Refresh user data on each request
      if (token.id) {
        try {
          await Database.getInstance().connect()
          const dbUser = await User.findById(token.id)
          
          if (dbUser) {
            token.subscription = dbUser.subscription
            token.credits = dbUser.credits
            token.preferences = dbUser.preferences
          }
        } catch (error) {
          console.error('JWT refresh error:', error)
        }
      }
      
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.provider = token.provider as 'google' | 'email'
        session.user.subscription = token.subscription as any
        session.user.credits = token.credits as any
        session.user.preferences = token.preferences as any
      }
      
      return session
    }
  },
  pages: {
    signIn: '/signin'
  },
  secret: process.env.NEXTAUTH_SECRET,
}

export default NextAuth(authOptions)