import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuthStore } from '../stores/authStore';
import { Search, Download, Copy, Eye, RotateCcw, Home, User, LogOut, Filter, Calendar } from 'lucide-react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface Generation {
  _id: string;
  prompt: string;
  code: string;
  createdAt: string;
  userId: string;
}

export default function History() {
  const [searchTerm, setSearchTerm] = useState("");
  const [generations, setGenerations] = useState<Generation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [filterBy, setFilterBy] = useState('all'); // all, today, week, month
  const [expandedCode, setExpandedCode] = useState<string | null>(null);
  
  const { user, logout, token } = useAuthStore();
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      navigate('/signin');
      return;
    }
    fetchHistory();
  }, [user, token, navigate]);

  const fetchHistory = async () => {
    if (!token) {
      setError('No authentication token found');
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      const response = await fetch('/api/code/history', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setGenerations(data.data || []);
      } else {
        setError('Failed to fetch history');
      }
    } catch (error) {
      setError('Failed to fetch history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  const filterGenerations = () => {
    let filtered = generations;
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(gen => 
        gen.prompt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        gen.code.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply date filter
    const now = new Date();
    if (filterBy === 'today') {
      filtered = filtered.filter(gen => {
        const genDate = new Date(gen.createdAt);
        return genDate.toDateString() === now.toDateString();
      });
    } else if (filterBy === 'week') {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      filtered = filtered.filter(gen => new Date(gen.createdAt) >= weekAgo);
    } else if (filterBy === 'month') {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      filtered = filtered.filter(gen => new Date(gen.createdAt) >= monthAgo);
    }
    
    return filtered;
  };

  const copyToClipboard = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  const exportToFile = (generation: Generation) => {
    const blob = new Blob([generation.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `vba-script-${generation._id}.vba`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const regenerateCode = (prompt: string) => {
    navigate('/generate', { state: { prompt } });
  };

  const filteredGenerations = filterGenerations();
  


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="brand">
                <span className="brand-logo">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                    <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                    <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
                  </svg>
                </span>
                <span className="brand-text">GenerateVBA</span>
              </Link>
            </div>
            <nav className="flex items-center space-x-4">
              <Link to="/dashboard" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium">
                <Home className="h-4 w-4 mr-1" />
                Dashboard
              </Link>
              <Link to="/generate" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium">
                <RotateCcw className="h-4 w-4 mr-1" />
                Generate
              </Link>
              <Link to="/profile" className="flex items-center text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium">
                <User className="h-4 w-4 mr-1" />
                Profile
              </Link>
              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm text-gray-600">Hi, {user?.name || 'User'}</span>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-700 hover:text-red-600 px-3 py-2 rounded-md text-sm font-medium"
                >
                  <LogOut className="h-4 w-4 mr-1" />
                  Logout
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Your VBA History</h1>
            <p className="text-gray-600">Browse, search, and reuse your previously generated VBA scripts.</p>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <div className="flex-1 max-w-md">
                  <label htmlFor="search" className="sr-only">Search</label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="search"
                      type="text"
                      className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                      placeholder="Search your VBA scripts..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Filter className="h-5 w-5 text-gray-400" />
                  <select
                    value={filterBy}
                    onChange={(e) => setFilterBy(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                  >
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                  </select>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => {
                    const dataStr = JSON.stringify(filteredGenerations, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'vba-history.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                  }}
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export All
                </button>
                <Link
                  to="/generate"
                  className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-200"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Generate New
                </Link>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              <span className="ml-2 text-gray-600">Loading your VBA history...</span>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <div className="text-red-500 mr-2">⚠️</div>
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {/* Results */}
          {!isLoading && !error && (
            <div className="space-y-6">
              {filteredGenerations.length === 0 ? (
                <div className="bg-white rounded-lg shadow p-12 text-center">
                  <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
                    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No scripts found</h3>
                  <p className="text-gray-500 mb-6">
                    {searchTerm ? "Try adjusting your search terms or filters." : "You haven't generated any VBA scripts yet."}
                  </p>
                  <Link
                    to="/generate"
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-pink-700 transition-all duration-200"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Generate Your First Script
                  </Link>
                </div>
              ) : (
                filteredGenerations.map((generation) => (
                  <div key={generation._id} className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          VBA Script
                        </h3>
                        <p className="text-gray-600 mb-3 line-clamp-2">
                          {generation.prompt}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {formatDate(generation.createdAt)}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2 ml-4">
                        <button
                          onClick={() => copyToClipboard(generation.code)}
                          className="flex items-center px-3 py-1.5 text-sm text-gray-600 hover:text-purple-600 border border-gray-300 rounded-lg hover:border-purple-300 transition-colors"
                          title="Copy to clipboard"
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copy
                        </button>
                        <button
                          onClick={() => setExpandedCode(expandedCode === generation._id ? null : generation._id)}
                          className="flex items-center px-3 py-1.5 text-sm text-gray-600 hover:text-purple-600 border border-gray-300 rounded-lg hover:border-purple-300 transition-colors"
                          title="Toggle code view"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {expandedCode === generation._id ? 'Hide' : 'View'}
                        </button>
                        <button
                          onClick={() => exportToFile(generation)}
                          className="flex items-center px-3 py-1.5 text-sm text-gray-600 hover:text-purple-600 border border-gray-300 rounded-lg hover:border-purple-300 transition-colors"
                          title="Export to file"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Export
                        </button>
                        <button
                          onClick={() => regenerateCode(generation.prompt)}
                          className="flex items-center px-3 py-1.5 text-sm text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200"
                          title="Regenerate with same prompt"
                        >
                          <RotateCcw className="h-4 w-4 mr-1" />
                          Regenerate
                        </button>
                      </div>
                    </div>
                    
                    {/* Code Preview */}
                    {expandedCode === generation._id && (
                      <div className="mt-4 rounded-lg overflow-hidden">
                        <SyntaxHighlighter
                          language="vbnet"
                          style={vscDarkPlus}
                          customStyle={{
                            margin: 0,
                            padding: '1rem',
                            fontSize: '0.875rem',
                            lineHeight: '1.5',
                          }}
                          showLineNumbers={true}
                          wrapLines={true}
                        >
                          {generation.code}
                        </SyntaxHighlighter>
                      </div>
                    )}
                    
                    {/* Collapsed Code Preview */}
                    {expandedCode !== generation._id && (
                      <div className="mt-4 bg-gray-900 rounded-lg p-3 overflow-hidden">
                        <pre className="text-green-400 text-xs font-mono line-clamp-3">
                          <code>{generation.code}</code>
                        </pre>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          )}

          {/* Results Summary */}
          {!isLoading && !error && filteredGenerations.length > 0 && (
            <div className="mt-8 bg-white rounded-lg shadow p-4">
              <div className="text-sm text-gray-700 text-center">
                Showing <span className="font-medium">{filteredGenerations.length}</span> script{filteredGenerations.length !== 1 ? 's' : ''}
                {searchTerm && <span> matching "{searchTerm}"</span>}
                {filterBy !== 'all' && <span> from {filterBy === 'today' ? 'today' : filterBy === 'week' ? 'this week' : 'this month'}</span>}
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}