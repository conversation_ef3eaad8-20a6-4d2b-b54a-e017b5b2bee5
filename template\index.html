<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Frameish - Turn a single photo into a stunning video</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&family=Instrument+Sans:wght@400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="./styles.css"/>
</head>
<body>
  <header class="site-header">
    <div class="container nav">
      <a class="brand" href="#">
        <span class="brand-logo">
          <!-- simple play-square logo -->
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
            <rect x="2" y="2" width="20" height="20" rx="5" fill="#a855f7"/>
            <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
            <path d="M10 9.2v5.6l5-2.8-5-2.8Z" fill="#a855f7"/>
          </svg>
        </span>
        <span class="brand-text">Frameish</span>
      </a>
      <nav class="links">
        <a href="#how">How to</a>
        <a href="#faqs">FAQs</a>
        <a href="#testimonials">Testimonials</a>
        <a href="#contact">Contact Us</a>
      </nav>
      <a class="cta ghost" href="#create">Create your own</a>
    </div>
  </header>

  <main>
    <!-- Decorative floating stickers -->
    <div class="sticker sticker-wand">
      <span class="sticker-emoji">✨</span>
    </div>
    <div class="sticker sticker-clip">
      <!-- tiny film icon -->
      <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
        <rect x="2" y="5" width="20" height="14" rx="3" fill="white" stroke="#e5e7eb"/>
        <rect x="7" y="8" width="10" height="8" rx="1.5" fill="#111827"/>
        <rect x="4" y="7" width="2" height="2" rx="0.5" fill="#9ca3af"/>
        <rect x="4" y="11" width="2" height="2" rx="0.5" fill="#9ca3af"/>
        <rect x="18" y="7" width="2" height="2" rx="0.5" fill="#9ca3af"/>
        <rect x="18" y="11" width="2" height="2" rx="0.5" fill="#9ca3af"/>
      </svg>
    </div>
    <div class="sticker sticker-avatar">
      <div class="avatar">
        <img src="https://images.unsplash.com/photo-1544006659-f0b21884ce1d?q=80&w=128&h=128&fit=crop&crop=faces" alt="avatar"/>
      </div>
    </div>

    <section class="hero container">
      <h1 class="hero-title">
        Turn a single photo<br/>
        into a <span class="gradient-text underline">stunning video</span>
      </h1>
      <p class="hero-sub">
        Upload a pic, drop in your prompt and watch the magic unfold! Our
        clever tech transforms your image into a unique video — whether
        it's epic, funny, cinematic, or just plain wild. You dream it, we
        animate it.
      </p>

      <div class="hero-cta">
        <a class="cta solid" href="#buy">
          <span class="cam-dot">🎥</span>
          Get 3 vids for $14.99
        </a>
      </div>

      <p class="mini-note">
        Money-Back Promise, No Sweat! – Not thrilled? We've got your back with a full refund, easy-peasy.
      </p>
    </section>

    <section id="how" class="steps">
      <div class="container">
        <h2 class="steps-title">
          Transform your images with<br/>
          <span class="brand-accent">three easy steps</span>
        </h2>

        <div class="steps-grid">
          <div class="step">
            <h3>Buy Credits</h3>
            <p>Grab your pass to the fun.</p>
          </div>
          <div class="step">
            <h3>Upload a photo</h3>
            <p>Pick your favorite person.</p>
          </div>
          <div class="step">
            <h3>Generate & Enjoy</h3>
            <p>Sit back while your creation comes to life.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Gallery / Examples -->
    <section class="examples" id="examples">
      <div class="container">
        <h2 class="section-title">
          See the Magic in <span class="gradient-text underline">Action</span>
        </h2>
        <p class="section-sub">
          Real examples of what you can create. Cool, quirky, and endlessly creative — all starting with a single photo and your wild ideas.
        </p>

        <div class="example-media">
          <img src="https://media.giphy.com/media/v1.Y2lkPTc5MGI3NjExb3Z2YjE5Z3NqM2xqM2NqMHB2a3ZneWJxcWx0Zmc4c2ZyZ2pxZmdkZiZlcD12MV9naWZzX3NlYXJjaCZjdD1n/13HgwGsXF0aiGY/giphy.gif" alt="demo" />
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials" id="testimonials">
      <div class="container">
        <h2 class="section-title alt">See what people are saying</h2>
        <p class="section-sub">
          Join thousands of satisfied users who have transformed their images with our tool
        </p>

        <div class="cards">
          <article class="card">
            <div class="stars">★★★★★</div>
            <p>Turning my selfies into short playful videos was so much fun! I love how easy it was — just upload, click, and let the magic happen. A perfect way to surprise my friends!</p>
            <div class="person">
              <img src="https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?q=80&w=64&h=64&fit=crop&crop=faces" alt="Jenna"/>
              <div><strong>Jenna Morales</strong><span>Content Creator</span></div>
            </div>
          </article>

          <article class="card">
            <div class="stars">★★★★★</div>
            <p>As a marketer, I'm constantly looking for tools that add a spark to my campaigns. These animated videos from still images are attention-grabbing and quick to produce!</p>
            <div class="person">
              <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=64&h=64&fit=crop&crop=faces" alt="Derek"/>
              <div><strong>Derek Sloan</strong><span>Marketing Strategist</span></div>
            </div>
          </article>

          <article class="card">
            <div class="stars">★★★★★</div>
            <p>This tool is incredible! It saved me hours of editing by turning a simple photo into a fun, cheeky video in just seconds. It's now my go-to for creating something memorable.</p>
            <div class="person">
              <img src="https://images.unsplash.com/photo-1544006659-443bbec07bcd?q=80&w=64&h=64&fit=crop&crop=faces" alt="Maya"/>
              <div><strong>Maya Lin</strong><span>Design Student</span></div>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- FAQ -->
    <section class="faq" id="faqs">
      <div class="container">
        <h2 class="section-title">
          Got Questions? <span class="gradient-text underline">We've Got Answers</span>
        </h2>
        <p class="section-sub">
          Find answers to common questions about our video creation tool and discover how it can transform your images.
        </p>

        <div class="accordion">
          <details open>
            <summary><span class="num">1</span> How does the image-to-video creation work?</summary>
            <p>Upload your photo, set a vibe, and our tech whips up a playful 5 second video with flair. It's quick, easy, and seriously fun.</p>
          </details>
          <details>
            <summary><span class="num">2</span> What image formats are supported?</summary>
            <p>JPEG and PNG images up to 10MB work best.</p>
          </details>
          <details>
            <summary><span class="num">3</span> Can I use the videos on social media?</summary>
            <p>Absolutely. Download and share anywhere.</p>
          </details>
          <details>
            <summary><span class="num">4</span> How fast is the generation process?</summary>
            <p>Most videos are ready in under 30 seconds.</p>
          </details>
          <details>
            <summary><span class="num">5</span> What kind of photos work best?</summary>
            <p>Clear, front-facing portraits with good lighting give the best results.</p>
          </details>
          <details>
            <summary><span class="num">6</span> Do I need an app?</summary>
            <p>No app needed — everything runs in the browser.</p>
          </details>
          <details>
            <summary><span class="num">7</span> What makes these videos special?</summary>
            <p>Our playful motion presets add cinematic flair while keeping your photo the star.</p>
          </details>
        </div>
      </div>
    </section>

    <!-- CTA Banner -->
    <section class="cta-banner" id="create">
      <div class="container banner">
        <div class="banner-copy">
          <h3>Ready to Animate Your Photos?</h3>
          <p>Turn any picture into a playful, shareable video in seconds — no app, no sign-up, just pure fun!</p>
        </div>
        <a class="cta ghost banner-btn" href="#buy">Start Now →</a>
      </div>
    </section>

    <!-- Footer -->
    <footer class="site-footer" id="contact">
      <div class="container foot">
        <div class="foot-brand">
          <div class="brand">
            <span class="brand-logo">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="2" y="2" width="20" height="20" rx="5" fill="#a855f7"/>
                <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                <path d="M10 9.2v5.6l5-2.8-5-2.8Z" fill="#a855f7"/>
              </svg>
            </span>
            <span class="brand-text">Frameish</span>
          </div>
          <p class="foot-tag">Turn photos into fun AI-generated videos instantly.</p>
          <div class="stats">
            <span><b>30s</b> Avg. Creation</span>
            <span><b>480p</b> Output</span>
            <span><b>Fun</b> No App Needed</span>
          </div>
        </div>
        <div class="legal">© 2025 Frameish · <a href="#">Terms of Service</a> · <a href="#">Privacy Policy</a></div>
      </div>
    </footer>
  </main>

  <script src="./script.js"></script>
</body>
</html>
