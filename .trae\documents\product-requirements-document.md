# Product Requirements Document

## 1. Product Overview

GenerateVBA.codes is an AI-powered micro-SaaS platform that enables office professionals and Excel power users to generate VBA macros instantly via natural language prompts. The platform transforms complex VBA coding into simple, conversational requests, making automation accessible to users without programming backgrounds.

The product solves the time-consuming challenge of manual VBA coding and debugging, targeting finance professionals and office administrators who need rapid automation solutions. With a lean one-person operation model, the platform aims to grow from $60 to $200 MRR within 6 months while maintaining high code accuracy and user satisfaction.

## 2. Core Features

### 2.1 User Roles

| Role         | Registration Method         | Core Permissions                                                    |
| ------------ | --------------------------- | ------------------------------------------------------------------- |
| Free User    | Email registration          | 5 credits/month, basic VBA generation                               |
| Premium User | Stripe subscription upgrade | Unlimited credits, advanced features, priority support              |
| Admin        | Internal access             | User management, analytics, system configuration, AI model settings |

### 2.2 Feature Module

Our VBA generator platform consists of the following main pages:

1. **Landing Page**: hero section with value proposition, feature highlights, pricing overview, testimonials
2. **Signup/Login Page**: NextAuth Google OAuth integration, JWT token management, session handling
3. **Dashboard**: credit usage tracking, generation history, quick access to new generation
4. **Code Generator**: natural language prompt input, real-time VBA code generation, syntax highlighting, copy/download functionality
5. **History Page**: searchable prompt history, code regeneration, export options
6. **Billing Page**: subscription management, payment methods, usage analytics
7. **Profile Page**: account settings, API preferences, notification settings

### 2.3 Page Details

| Page Name         | Module Name         | Feature description                                                                                    |
| ----------------- | ------------------- | ------------------------------------------------------------------------------------------------------ |
| Landing Page      | Hero Section        | Display value proposition with gradient text effects, animated stickers, CTA buttons leading to signup |
| Landing Page      | Features Section    | Showcase core capabilities with step-by-step process, visual examples of VBA generation                |
| Landing Page      | Testimonials        | User reviews with star ratings, profile images, role descriptions                                      |
| Landing Page      | FAQ Accordion       | Expandable questions about VBA generation, pricing, technical requirements                             |
| Signup/Login Page | Google OAuth Button | Large "Continue with Google" button with Google branding, NextAuth integration                         |
| Signup/Login Page | Authentication Form | Email/password fallback option, form validation, error handling                                        |
| Signup/Login Page | Session Management  | JWT token creation, secure cookie storage, automatic session refresh                                   |
| Signup/Login Page | Onboarding Flow     | New user welcome, initial credit allocation, tutorial guidance                                         |
| Dashboard         | Usage Overview      | Display remaining credits, monthly usage charts, quick stats with gradient backgrounds                 |
| Dashboard         | Recent History      | List last 5 generated codes with timestamps, quick copy buttons, regenerate options                    |
| Dashboard         | Quick Generate      | Prominent input field for new VBA requests, instant generation button                                  |
| Code Generator    | Prompt Input        | Large textarea with character counter (500 max), example prompts, submit button                        |
| Code Generator    | Code Output         | Syntax-highlighted VBA code display, copy button, download .bas file option                            |
| Code Generator    | Code Preview        | Live preview with error checking, formatting options, test functionality                               |
| History Page      | Search & Filter     | Search prompts by keywords, filter by date range, sort by usage frequency                              |
| History Page      | Code Library        | Paginated list of all generated codes, tags, favorites, export bulk options                            |
| Billing Page      | Subscription Plans  | Current plan display, upgrade/downgrade options, billing cycle management                              |
| Billing Page      | Payment Methods     | Credit card management, invoice history, automatic renewal settings                                    |
| Profile Page      | Account Settings    | Email preferences, password change, account deletion options                                           |
| Profile Page      | API Configuration   | Custom templates, code formatting preferences (model settings admin-only)                              |

## 3. Core Process

**User Registration & Onboarding Flow:**
New users land on the homepage, click "Get Started", are redirected to the signup page with Google OAuth integration. Users authenticate via Google (preferred) or email/password, NextAuth creates a JWT session, new users receive 5 free credits, and are guided through a quick tutorial showing how to generate their first VBA macro.

**Authentication & Session Management Flow:**
Users access protected routes, NextAuth middleware validates JWT tokens, expired tokens trigger automatic refresh, failed authentication redirects to login page, successful authentication maintains session state across all platform pages.

**VBA Generation Flow:**
Authenticated users access the dashboard, click "Generate New Code", enter a natural language prompt (e.g., "Loop through Sheet1 from A1 to A100 and sum values"), submit the request, receive syntactically correct VBA code within 2 seconds, and can copy or download the result.

**Subscription Management Flow:**
Users monitor credit usage on dashboard, receive low-credit notifications, access billing page to upgrade plans, complete Stripe payment process, and immediately receive additional credits.

```mermaid
graph TD
  A[Landing Page] --> B[Signup/Login Page]
  B --> C[Google OAuth]
  B --> D[Email/Password Auth]
  C --> E[JWT Session Creation]
  D --> E
  E --> F[Dashboard]
  F --> G[Code Generator]
  G --> H[Generated Code]
  H --> I[History Page]
  F --> J[Billing Page]
  J --> K[Subscription Upgrade]
  F --> L[Profile Settings]
  I --> G
  L --> M[Logout]
  M --> A
```

## 4. User Interface Design

### 4.1 Design Style

* **Primary Colors**: Purple (#7c3aed, #6d28d9) and Pink (#ec4899, #ea4aaa)

* **Secondary Colors**: Gray scale (#f3f4f6, #6b7280, #0f172a)

* **Button Style**: Rounded corners (28px border-radius), gradient backgrounds, subtle shadows

* **Font**: Inter font family with weights 400-900, clean and modern typography

* **Layout Style**: Card-based design with soft shadows, sticky navigation, container max-width 1000px

* **Icons**: Minimalist SVG icons, code-related emojis (💻, ⚡, 📊), consistent 24px sizing

### 4.2 Page Design Overview

| Page Name         | Module Name       | UI Elements                                                                                                                       |
| ----------------- | ----------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| Landing Page      | Hero Section      | Large gradient text (64px), purple-pink gradient background, floating sticker animations, prominent CTA button with hover effects |
| Landing Page      | Features Grid     | 3-column responsive grid, card backgrounds (#fafafa), rounded corners (22px), subtle box shadows                                  |
| Dashboard         | Stats Cards       | White background cards with colored accents, usage charts with purple gradients, credit counters with large bold numbers          |
| Code Generator    | Input Area        | Large textarea with focus ring effects, character counter, example prompts in muted text                                          |
| Code Generator    | Output Panel      | Dark code background (#111827), syntax highlighting, copy button with success animation                                           |
| History Page      | Search Bar        | Rounded input with search icon, filter dropdowns with purple accents                                                              |
| Billing Page      | Plan Cards        | Highlighted current plan, upgrade buttons with gradient backgrounds, pricing in large bold text                                   |
| Signup/Login Page | OAuth Button      | Large Google OAuth button with white background, Google logo, "Continue with Google" text, hover shadow effects                   |
| Signup/Login Page | Auth Form         | Clean form layout with rounded inputs, purple focus rings, validation messages, "Sign in with Email" option                       |
| Signup/Login Page | Loading States    | Spinner animations during authentication, success/error toast notifications                                                       |
| Signup/Login Page | Security Features | CSRF protection indicators, secure session badges, privacy policy links                                                           |

### 4.3 Responsiveness

The platform is desktop-first with mobile-adaptive design. Navigation collapses to hamburger menu on screens below 900px. Touch interactions are optimized for mobile code copying and plan selection. All cards and grids stack vertically on mobile devices while maintaining visual hierarchy and accessibility.

## 5. Authentication & Security

### 5.1 NextAuth JWT Implementation

* **JWT Strategy**: 30-day token expiration with automatic refresh

* **Session Management**: Secure HTTP-only cookies, CSRF protection enabled

* **Google OAuth 2.0**: Primary authentication method with profile data sync

* **Fallback Authentication**: Email/password option with bcrypt hashing

* **Security Headers**: Content Security Policy, HTTPS enforcement, secure cookie flags

### 5.2 User Session Flow

1. **Initial Authentication**: User clicks "Continue with Google" → OAuth consent → JWT token creation
2. **Session Validation**: Middleware checks JWT on protected routes → Auto-refresh if near expiration
3. **Logout Process**: Clear JWT tokens → Revoke Google access → Redirect to landing page
4. **Security Monitoring**: Track failed login attempts → Rate limiting → Suspicious activity alerts

### 5.3 Data Protection

* **Personal Data**: Email, name, and profile image stored securely in MongoDB

* **OAuth Tokens**: Encrypted storage of refresh tokens and access tokens

* **GDPR Compliance**: User data export/deletion capabilities in profile settings

* **Audit Logging**: Authentication events, session activities, and security incidents tracked

