import { Router, type Request, type Response } from 'express';
import OpenAI from 'openai';
import CodeGeneration from '../models/CodeGeneration.js';
import User from '../models/User.js';
import { authenticateSession, checkCredits, AuthRequest } from '../utils/nextauth-helpers.js';

const router = Router();

// Initialize OpenAI
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

/**
 * Generate VBA code
 * POST /api/code/generate
 */
router.post('/generate', authenticateSession, checkCredits, async (req: AuthRequest, res: Response): Promise<void> => {
  const startTime = Date.now();
  
  try {
    const { prompt, language = 'vba', tags = [] } = req.body;

    if (!prompt || prompt.trim().length === 0) {
      res.status(400).json({
        success: false,
        error: 'Prompt is required'
      });
      return;
    }

    if (prompt.length > 2000) {
      res.status(400).json({
        success: false,
        error: 'Prompt too long (max 2000 characters)'
      });
      return;
    }

    // Create initial code generation record
    const codeGeneration = new CodeGeneration({
      userId: req.user!.userId,
      prompt: prompt.trim(),
      language,
      tags,
      generatedCode: '',
      status: 'pending'
    });

    await codeGeneration.save();

    try {
      // Prepare system message based on language
      const systemMessages = {
        'vba': 'You are an expert VBA programmer. Generate clean, well-commented VBA code based on the user\'s requirements. Include error handling where appropriate.',
        'excel-vba': 'You are an expert Excel VBA programmer. Generate Excel-specific VBA code with proper worksheet and range handling. Include error handling and comments.',
        'word-vba': 'You are an expert Word VBA programmer. Generate Word-specific VBA code for document manipulation. Include error handling and comments.',
        'access-vba': 'You are an expert Access VBA programmer. Generate Access-specific VBA code for database operations. Include error handling and comments.'
      };

      const systemMessage = systemMessages[language as keyof typeof systemMessages] || systemMessages.vba;

      // Generate code using OpenAI
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemMessage
          },
          {
            role: 'user',
            content: `Generate VBA code for: ${prompt}`
          }
        ],
        max_tokens: 2000,
        temperature: 0.7
      });

      const generatedCode = completion.choices[0]?.message?.content || '';
      const tokensUsed = completion.usage?.total_tokens || 0;
      const executionTime = Date.now() - startTime;

      // Update code generation record
      codeGeneration.generatedCode = generatedCode;
      codeGeneration.status = 'completed';
      codeGeneration.metadata = {
        tokensUsed,
        executionTime,
        model: 'gpt-3.5-turbo'
      };
      await codeGeneration.save();

      // Update user credits
      await User.findByIdAndUpdate(req.user!.userId, {
        $inc: {
          'credits.used': 1,
          'credits.remaining': -1
        }
      });

      res.json({
        success: true,
        data: {
          id: codeGeneration._id,
          code: generatedCode,
          metadata: {
            tokensUsed,
            executionTime,
            model: 'gpt-3.5-turbo'
          }
        }
      });

    } catch (openaiError) {
      console.error('OpenAI API error:', openaiError);
      
      // Update code generation record with error
      codeGeneration.status = 'failed';
      await codeGeneration.save();

      res.status(500).json({
        success: false,
        error: 'Failed to generate code. Please try again.'
      });
    }

  } catch (error) {
    console.error('Code generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * Get user's code generation history
 * GET /api/code/history
 */
router.get('/history', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { page = 1, limit = 10, language, search } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { userId: req.user!.userId };
    if (language) query.language = language;
    if (search) {
      query.$or = [
        { prompt: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search as string, 'i')] } }
      ];
    }

    const [generations, total] = await Promise.all([
      CodeGeneration.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit))
        .select('-__v'),
      CodeGeneration.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        generations,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  } catch (error) {
    console.error('Get history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get code generation history'
    });
  }
});

/**
 * Get specific code generation
 * GET /api/code/:id
 */
router.get('/:id', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const codeGeneration = await CodeGeneration.findOne({
      _id: id,
      userId: req.user!.userId
    }).select('-__v');

    if (!codeGeneration) {
      res.status(404).json({
        success: false,
        error: 'Code generation not found'
      });
      return;
    }

    res.json({
      success: true,
      data: codeGeneration
    });
  } catch (error) {
    console.error('Get code generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get code generation'
    });
  }
});

/**
 * Delete code generation
 * DELETE /api/code/:id
 */
router.delete('/:id', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const codeGeneration = await CodeGeneration.findOneAndDelete({
      _id: id,
      userId: req.user!.userId
    });

    if (!codeGeneration) {
      res.status(404).json({
        success: false,
        error: 'Code generation not found'
      });
      return;
    }

    res.json({
      success: true,
      message: 'Code generation deleted successfully'
    });
  } catch (error) {
    console.error('Delete code generation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete code generation'
    });
  }
});

export default router;