import Stripe from 'stripe';
import { PaymentProvider, PaymentCustomer, PaymentSubscription, CheckoutSession, WebhookEvent, PaymentPlan } from '../types.js';
import { paymentConfig } from '../config.js';

export class StripeProvider implements PaymentProvider {
  private stripe: Stripe;
  private webhookSecret: string;
  private proPriceId: string;

  constructor() {
    const secretKey = process.env.STRIPE_SECRET_KEY;
    this.webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || '';
    this.proPriceId = process.env.STRIPE_PRO_PRICE_ID || '';

    if (!secretKey) {
      throw new Error('STRIPE_SECRET_KEY is required');
    }

    if (!this.webhookSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET is required');
    }

    if (!this.proPriceId) {
      throw new Error('STRIPE_PRO_PRICE_ID is required');
    }

    this.stripe = new Stripe(secretKey, {
      apiVersion: '2025-07-30.basil'
    });
  }

  async createCustomer(data: {
    email: string;
    name: string;
    metadata?: Record<string, string>;
  }): Promise<PaymentCustomer> {
    const customer = await this.stripe.customers.create({
      email: data.email,
      name: data.name,
      metadata: data.metadata
    });

    return {
      id: customer.id,
      email: customer.email || data.email,
      name: customer.name || data.name,
      metadata: customer.metadata
    };
  }

  async getCustomer(customerId: string): Promise<PaymentCustomer> {
    const customer = await this.stripe.customers.retrieve(customerId) as Stripe.Customer;

    return {
      id: customer.id,
      email: customer.email || '',
      name: customer.name || '',
      metadata: customer.metadata
    };
  }

  async createCheckoutSession(data: {
    customerId: string;
    planId: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<CheckoutSession> {
    if (data.planId !== 'pro') {
      throw new Error('Only pro plan is available for checkout');
    }

    const session = await this.stripe.checkout.sessions.create({
      customer: data.customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: this.proPriceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: data.successUrl,
      cancel_url: data.cancelUrl,
      metadata: {
        ...data.metadata,
        planId: data.planId
      }
    });

    return {
      id: session.id,
      url: session.url || '',
      customerId: data.customerId,
      planId: data.planId,
      metadata: session.metadata || undefined
    };
  }

  async getSubscription(subscriptionId: string): Promise<PaymentSubscription> {
    const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);

    return {
      id: subscription.id,
      customerId: subscription.customer as string,
      planId: 'pro', // We only have pro plan for subscriptions
      status: this.mapStripeStatus(subscription.status),
      currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
      currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end
    };
  }

  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<PaymentSubscription> {
    const subscription = await this.stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd
    });

    return {
      id: subscription.id,
      customerId: subscription.customer as string,
      planId: 'pro',
      status: this.mapStripeStatus(subscription.status),
      currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
      currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
      cancelAtPeriodEnd: (subscription as any).cancel_at_period_end
    };
  }

  async constructWebhookEvent(payload: string | Buffer, signature: string, secret?: string): Promise<WebhookEvent> {
    const webhookSecret = secret || this.webhookSecret;
    const event = this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);

    return {
      id: event.id,
      type: event.type,
      data: event.data,
      created: new Date(event.created * 1000)
    };
  }

  async getPlans(): Promise<PaymentPlan[]> {
    return paymentConfig.getPlans();
  }

  private mapStripeStatus(status: Stripe.Subscription.Status): 'active' | 'canceled' | 'past_due' | 'incomplete' {
    switch (status) {
      case 'active':
        return 'active';
      case 'canceled':
        return 'canceled';
      case 'past_due':
        return 'past_due';
      case 'incomplete':
      case 'incomplete_expired':
      case 'unpaid':
        return 'incomplete';
      default:
        return 'incomplete';
    }
  }
}