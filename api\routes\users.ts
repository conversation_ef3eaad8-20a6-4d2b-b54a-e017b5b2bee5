import { Router, type Request, type Response } from 'express';
import User, { IUser } from '../models/User.js';
import { authenticateSession, requireAdmin, AuthRequest } from '../utils/nextauth-helpers.js';

const router = Router();

/**
 * Get current user (alias for profile)
 * GET /api/users/me
 */
router.get('/me', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user?.userId).select('-__v');
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user data'
    });
  }
});

/**
 * Get current user profile
 * GET /api/users/profile
 */
router.get('/profile', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user?.userId).select('-__v');
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user profile'
    });
  }
});

/**
 * Update user profile
 * PUT /api/users/profile
 */
router.put('/profile', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { name, preferences } = req.body;
    
    const updateData: Partial<IUser> = {};
    if (name) updateData.name = name;
    if (preferences) updateData.preferences = preferences;

    const user = await User.findByIdAndUpdate(
      req.user?.userId,
      updateData,
      { new: true, runValidators: true }
    ).select('-__v');

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user profile'
    });
  }
});

/**
 * Get user credits/**
 * GET /api/users/credits
 */
router.get('/credits', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user?.userId).select('credits');
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user.credits
    });
  } catch (error) {
    console.error('Get credits error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get user credits'
    });
  }
});

/**
 * Update user credits (admin only)
 * PUT /api/users/:userId/credits
 */
router.put('/:userId/credits', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { userId } = req.params;
    const { credits } = req.body;

    // TODO: Add admin role check
    
    const user = await User.findByIdAndUpdate(
      userId,
      { credits },
      { new: true, runValidators: true }
    ).select('credits');

    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: user.credits
    });
  } catch (error) {
    console.error('Update credits error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update user credits'
    });
  }
});

export default router;