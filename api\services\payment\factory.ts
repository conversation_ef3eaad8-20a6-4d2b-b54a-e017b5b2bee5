import { PaymentProvider } from './types.js';
import { StripeProvider } from './providers/stripe.js';
import { DodoPaymentsProvider } from './providers/dodopayments.js';
import { paymentConfig } from './config.js';

export class PaymentProviderFactory {
  private static instance: PaymentProvider | null = null;

  static getProvider(): PaymentProvider {
    if (!this.instance) {
      this.instance = this.createProvider();
    }
    return this.instance;
  }

  static resetProvider(): void {
    this.instance = null;
  }

  private static createProvider(): PaymentProvider {
    const providerType = paymentConfig.getProvider();

    switch (providerType) {
      case 'stripe':
        return new StripeProvider();
      case 'dodopayments':
        return new DodoPaymentsProvider();
      default:
        throw new Error(`Unsupported payment provider: ${providerType}`);
    }
  }
}

// Export a convenience function to get the current provider
export const getPaymentProvider = (): PaymentProvider => {
  return PaymentProviderFactory.getProvider();
};