export interface PaymentPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  credits: number | 'unlimited';
  features: string[];
  interval?: 'month' | 'year';
}

export interface PaymentCustomer {
  id: string;
  email: string;
  name: string;
  metadata?: Record<string, string>;
}

export interface PaymentSubscription {
  id: string;
  customerId: string;
  planId: string;
  status: 'active' | 'canceled' | 'past_due' | 'incomplete';
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd?: boolean;
}

export interface CheckoutSession {
  id: string;
  url: string;
  customerId: string;
  planId: string;
  metadata?: Record<string, string>;
}

export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  created: Date;
}

export interface PaymentProvider {
  // Customer management
  createCustomer(data: {
    email: string;
    name: string;
    metadata?: Record<string, string>;
  }): Promise<PaymentCustomer>;

  getCustomer(customerId: string): Promise<PaymentCustomer>;

  // Subscription management
  createCheckoutSession(data: {
    customerId: string;
    planId: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<CheckoutSession>;

  getSubscription(subscriptionId: string): Promise<PaymentSubscription>;

  cancelSubscription(subscriptionId: string, cancelAtPeriodEnd?: boolean): Promise<PaymentSubscription>;

  // Webhook handling
  constructWebhookEvent(payload: string | Buffer, signature: string, secret: string): Promise<WebhookEvent>;

  // Plan management
  getPlans(): Promise<PaymentPlan[]>;
}

export interface PaymentConfig {
  provider: 'stripe' | 'dodopayments';
  plans: {
    free: {
      credits: number;
    };
    pro: {
      price: number;
      currency: string;
      credits: number | 'unlimited';
    };
    enterprise: {
      credits: number | 'unlimited';
    };
  };
}