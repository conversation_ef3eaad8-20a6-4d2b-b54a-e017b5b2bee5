import { Link } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSession, signOut } from 'next-auth/react';
import { 
  Users, 
  Settings, 
  BarChart3, 
  Shield, 
  Search, 
  Download, 
  Eye, 
  Trash2, 
  Edit3,
  Filter,
  Al<PERSON><PERSON>riangle,
  TrendingUp,
  DollarSign,
  Activity,
  Target
} from "lucide-react";
import { toast } from "sonner";

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalGenerations: number;
  revenue: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  plan: string;
  joined: string;
  generations: number;
  status: 'active' | 'inactive';
}

interface Generation {
  id: string;
  user: string;
  prompt: string;
  status: 'Success' | 'Failed';
  createdAt: string;
  tokens: number;
}

export default function Admin() {
  const navigate = useNavigate();
  const { data: session, status } = useSession();
  const [activeTab, setActiveTab] = useState("users");
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeUsers: 0,
    totalGenerations: 0,
    revenue: 0
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  
  // Check if user is admin
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user) {
      navigate('/signin');
      return;
    }
    
    // Note: Admin role check would need to be implemented in your user model
    // For now, we'll check if user email is in admin list
    const adminEmails = process.env.NEXT_PUBLIC_ADMIN_EMAILS?.split(',') || [];
    if (!adminEmails.includes(session.user.email || '')) {
      navigate('/dashboard');
      toast.error('Access denied. Admin privileges required.');
      return;
    }
  }, [session, status, navigate]);

  // Fetch admin stats
  useEffect(() => {
    const fetchAdminStats = async () => {
      if (!session?.user) return;
      
      try {
        const response = await fetch('/api/admin/stats', {
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error('Failed to fetch admin stats:', error);
        toast.error('Failed to load admin statistics');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdminStats();
  }, [session]);

  // Fetch users data
  const fetchUsers = async () => {
    if (!session?.user) return [];
    
    try {
      const response = await fetch('/api/admin/users', {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Failed to fetch users:', error);
      toast.error('Failed to load users data');
    }
    return [];
  };

  // Fetch generations data
  const fetchGenerations = async () => {
    if (!session?.user) return [];
    
    try {
      const response = await fetch('/api/admin/generations', {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Failed to fetch generations:', error);
      toast.error('Failed to load generations data');
    }
    return [];
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    navigate('/');
    toast.success('Logged out successfully');
  };

  // Mock data for demonstration (will be replaced by API calls)

  const users = [
    { id: 1, name: "John Doe", email: "<EMAIL>", plan: "Premium", joined: "2024-12-15", generations: 45 },
    { id: 2, name: "Jane Smith", email: "<EMAIL>", plan: "Free", joined: "2024-12-20", generations: 5 },
    { id: 3, name: "Bob Johnson", email: "<EMAIL>", plan: "Premium", joined: "2024-12-10", generations: 78 }
  ];

  const generations = [
    { id: 1, user: "John Doe", prompt: "Create a data validation macro", status: "Success", createdAt: "2025-01-15 10:30" },
    { id: 2, user: "Jane Smith", prompt: "Generate report automation", status: "Success", createdAt: "2025-01-15 09:15" },
    { id: 3, user: "Bob Johnson", prompt: "Import CSV data script", status: "Failed", createdAt: "2025-01-15 08:45" }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-purple-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">VBA Generator Admin</h1>
            </div>
            <nav className="flex items-center space-x-8">
              <Link to="/dashboard" className="flex items-center text-gray-500 hover:text-gray-900 transition-colors">
                <BarChart3 className="h-4 w-4 mr-2" />
                Dashboard
              </Link>
              <Link to="/generator" className="flex items-center text-gray-500 hover:text-gray-900 transition-colors">
                <Activity className="h-4 w-4 mr-2" />
                Generator
              </Link>
              <Link to="/history" className="flex items-center text-gray-500 hover:text-gray-900 transition-colors">
                <TrendingUp className="h-4 w-4 mr-2" />
                History
              </Link>
              <Link to="/billing" className="flex items-center text-gray-500 hover:text-gray-900 transition-colors">
                <DollarSign className="h-4 w-4 mr-2" />
                Billing
              </Link>
              <Link to="/profile" className="flex items-center text-gray-500 hover:text-gray-900 transition-colors">
                <Settings className="h-4 w-4 mr-2" />
                Profile
              </Link>
              <div className="flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200">
                <span className="text-sm text-gray-600">Welcome, {session?.user?.name}</span>
                <button
                  onClick={handleLogout}
                  className="flex items-center text-gray-500 hover:text-gray-900 transition-colors"
                >
                  Logout
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Admin Dashboard</h1>
            <p className="text-gray-600">Monitor and manage the GenerateVBA platform.</p>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
                      ) : (
                        stats.totalUsers.toLocaleString()
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
                      ) : (
                        stats.activeUsers.toLocaleString()
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Generations</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
                      ) : (
                        stats.totalGenerations.toLocaleString()
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Revenue</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {isLoading ? (
                        <div className="animate-pulse bg-gray-200 h-6 w-16 rounded"></div>
                      ) : (
                        `$${stats.revenue.toLocaleString()}`
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="card">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8" aria-label="Tabs">
                <button
                  onClick={() => setActiveTab("users")}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === "users"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Users
                </button>
                <button
                  onClick={() => setActiveTab("generations")}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === "generations"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Target className="h-4 w-4 mr-2" />
                  Generations
                </button>
                <button
                  onClick={() => setActiveTab("analytics")}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === "analytics"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analytics
                </button>
                <button
                  onClick={() => setActiveTab("settings")}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === "settings"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </button>
              </nav>
            </div>

            <div className="p-6">
              {/* Users Tab */}
              {activeTab === "users" && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <Users className="h-6 w-6 text-purple-600 mr-3" />
                      <h2 className="text-xl font-bold">User Management</h2>
                    </div>
                    <div className="flex space-x-2">
                      <div className="relative">
                        <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          placeholder="Search users..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        />
                      </div>
                      <button className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </button>
                    </div>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generations</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {users.map((user) => (
                          <tr key={user.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">{user.name}</div>
                                <div className="text-sm text-gray-500">{user.email}</div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                user.plan === "Premium" ? "bg-purple-100 text-purple-800" : "bg-gray-100 text-gray-800"
                              }`}>
                                {user.plan}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.joined}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.generations}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-purple-600 hover:text-purple-900 p-1 rounded transition-colors">
                                  <Edit3 className="h-4 w-4" />
                                </button>
                                <button className="text-red-600 hover:text-red-900 p-1 rounded transition-colors">
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Generations Tab */}
              {activeTab === "generations" && (
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <Target className="h-6 w-6 text-purple-600 mr-3" />
                      <h2 className="text-xl font-bold">Recent Generations</h2>
                    </div>
                    <div className="flex space-x-2">
                      <div className="relative">
                        <Filter className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <select 
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                          className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500 appearance-none bg-white"
                        >
                          <option value="all">All Status</option>
                          <option value="success">Success</option>
                          <option value="failed">Failed</option>
                        </select>
                      </div>
                      <button className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </button>
                    </div>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prompt</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {generations.map((gen) => (
                          <tr key={gen.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{gen.user}</td>
                            <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">{gen.prompt}</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                gen.status === "Success" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                              }`}>
                                {gen.status}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{gen.createdAt}</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex items-center space-x-2">
                                <button className="text-purple-600 hover:text-purple-900 p-1 rounded transition-colors">
                                  <Eye className="h-4 w-4" />
                                </button>
                                <button className="text-red-600 hover:text-red-900 p-1 rounded transition-colors">
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {/* Analytics Tab */}
              {activeTab === "analytics" && (
                <div>
                  <div className="flex items-center mb-6">
                    <BarChart3 className="h-6 w-6 text-purple-600 mr-3" />
                    <h2 className="text-xl font-bold">Platform Analytics</h2>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-center mb-4">
                        <TrendingUp className="h-8 w-8 text-blue-500" />
                      </div>
                      <h3 className="font-semibold mb-2">Usage Trends</h3>
                      <p className="text-gray-600">Daily active users over time</p>
                      <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart Coming Soon</span>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-center mb-4">
                        <DollarSign className="h-8 w-8 text-green-500" />
                      </div>
                      <h3 className="font-semibold mb-2">Revenue Growth</h3>
                      <p className="text-gray-600">Monthly revenue trends</p>
                      <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart Coming Soon</span>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-center mb-4">
                        <Target className="h-8 w-8 text-purple-500" />
                      </div>
                      <h3 className="font-semibold mb-2">Generation Success Rate</h3>
                      <p className="text-gray-600">Success vs failure rates</p>
                      <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart Coming Soon</span>
                      </div>
                    </div>
                    <div className="bg-white border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-center mb-4">
                        <Activity className="h-8 w-8 text-orange-500" />
                      </div>
                      <h3 className="font-semibold mb-2">Popular Features</h3>
                      <p className="text-gray-600">Most used VBA categories</p>
                      <div className="mt-4 h-32 bg-gray-100 rounded flex items-center justify-center">
                        <span className="text-gray-500">Chart Coming Soon</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === "settings" && (
                <div>
                  <div className="flex items-center mb-6">
                    <Settings className="h-6 w-6 text-purple-600 mr-3" />
                    <h2 className="text-xl font-bold">Platform Settings</h2>
                  </div>
                  <div className="space-y-6">
                    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Settings className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h3 className="font-semibold mb-2">API Configuration</h3>
                          <p className="text-gray-600 mb-4">Manage OpenAI API settings and rate limits</p>
                          <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                            Configure API
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <Edit3 className="h-5 w-5 text-green-600" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h3 className="font-semibold mb-2">Email Templates</h3>
                          <p className="text-gray-600 mb-4">Customize email notifications and marketing templates</p>
                          <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                            Edit Templates
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <Shield className="h-5 w-5 text-purple-600" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h3 className="font-semibold mb-2">Security Settings</h3>
                          <p className="text-gray-600 mb-4">Configure authentication and security policies</p>
                          <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                            Security Settings
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                      <div className="flex items-start">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                          </div>
                        </div>
                        <div className="ml-4 flex-1">
                          <h3 className="font-semibold mb-2 text-red-600">Maintenance Mode</h3>
                          <p className="text-gray-600 mb-4">Enable maintenance mode to perform system updates</p>
                          <button className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                            Enable Maintenance
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}