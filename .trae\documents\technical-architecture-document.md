# Technical Architecture Document

## 1. Architecture Design

```mermaid
graph TD
  A[User Browser] --> B[Next.js Frontend Application]
  B --> C[Node.js/Express Backend API]
  C --> D[MongoDB Database]
  C --> E[OpenAI API Service]
  C --> F[Stripe Payment Service]
  C --> G[NextAuth Authentication]

  subgraph "Frontend Layer"
    B
  end

  subgraph "Backend Layer"
    C
    G
  end

  subgraph "Data Layer"
    D
  end

  subgraph "External Services"
    E
    F
  end
```

## 2. Technology Description

* Frontend: React\@18 + Next.js\@14 + Tailwind CSS\@3 + TypeScript

* Backend: Node.js\@18 + Express\@4 + Winston (logging) + Rate Limiting

* Database: MongoDB (users, prompts, usage logs, subscriptions)

* Authentication: NextAuth.js v4 + JWT strategy + Google OAuth 2.0 + CSRF protection

* Payments: Stripe API for subscription management

* AI Service: OpenAI GPT-4 API for VBA code generation

* Hosting: Vercel (frontend), Railway/Render (backend API)

## 3. Route Definitions

| Route           | Purpose                                                    |
| --------------- | ---------------------------------------------------------- |
| /               | Landing page with hero section, features, and pricing      |
| /login          | Authentication page with Google OAuth integration          |
| /signup         | User registration page with Google OAuth and email options |
| /auth/callback  | OAuth callback handler for Google authentication          |
| /auth/signin    | NextAuth signin page with multiple provider options       |
| /auth/signout   | Secure logout with session cleanup and token invalidation |
| /dashboard      | Main user dashboard with usage stats and quick generation  |
| /generate       | VBA code generation interface with prompt input and output |
| /history        | User's generation history with search and export features  |
| /billing        | Subscription management and payment method configuration   |
| /profile        | User account settings and API preferences                  |
| /admin          | Admin panel for system configuration and AI model settings |
| /api/auth/\*    | NextAuth authentication endpoints (signin, callback, session) |
| /api/auth/session | Get current user session and JWT token validation       |
| /api/auth/csrf  | CSRF token generation for secure form submissions         |
| /api/generate   | VBA code generation API endpoint                           |
| /api/user/\*    | User management and profile endpoints                      |
| /api/admin/\*   | Admin-only endpoints for system and AI model configuration |
| /api/billing/\* | Stripe webhook and subscription management                 |

## 4. API Definitions

### 4.1 Core API

**VBA Code Generation**

```
POST /api/generate
```

Request:

| Param Name | Param Type | isRequired | Description                                                       |
| ---------- | ---------- | ---------- | ----------------------------------------------------------------- |
| prompt     | string     | true       | Natural language description of VBA functionality (max 500 chars) |
| userId     | string     | true       | Authenticated user identifier                                     |
| options    | object     | false      | Generation preferences (complexity, style, comments)              |

Response:

| Param Name       | Param Type | Description                                 |
| ---------------- | ---------- | ------------------------------------------- |
| success          | boolean    | Generation status                           |
| code             | string     | Generated VBA code with syntax highlighting |
| creditsRemaining | number     | User's remaining credits after generation   |
| generationId     | string     | Unique identifier for this generation       |

Example Request:

```json
{
  "prompt": "Loop through Sheet1 from A1 to A100 and sum values",
  "userId": "user_123",
  "options": {
    "includeComments": true,
    "complexity": "intermediate"
  }
}
```

Example Response:

```json
{
  "success": true,
  "code": "Sub SumValues()\n    Dim sum As Double\n    Dim i As Integer\n    \n    For i = 1 To 100\n        sum = sum + Cells(i, 1).Value\n    Next i\n    \n    MsgBox \"Total sum: \" & sum\nEnd Sub",
  "creditsRemaining": 4,
  "generationId": "gen_456"
}
```

**User Credit Management**

```
GET /api/user/credits
POST /api/user/credits/deduct
```

**Generation History**

```
GET /api/user/history
POST /api/user/history/search
```

**NextAuth Authentication APIs**

```
GET /api/auth/session
POST /api/auth/signin/google
POST /api/auth/signout
GET /api/auth/csrf
```

**Session Validation**

```
GET /api/auth/session
```

Response:

| Param Name | Param Type | Description |
|------------|------------|--------------|
| user | object | Authenticated user information |
| expires | string | JWT token expiration timestamp |
| accessToken | string | JWT access token for API calls |

Example Response:

```json
{
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "image": "https://lh3.googleusercontent.com/...",
    "provider": "google"
  },
  "expires": "2024-01-01T00:00:00.000Z",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Google OAuth Signin**

```
POST /api/auth/signin/google
```

Request:

| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| callbackUrl | string | false | Redirect URL after successful authentication |
| csrfToken | string | true | CSRF protection token |

**Subscription Management**

```
POST /api/billing/create-subscription
POST /api/billing/webhook
GET /api/billing/invoices
```

## 5. Server Architecture Diagram

```mermaid
graph TD
  A[Client / Frontend] --> B[Authentication Middleware]
  B --> C[Rate Limiting Middleware]
  C --> D[Controller Layer]
  D --> E[Service Layer]
  E --> F[Repository Layer]
  F --> G[(MongoDB)]
  E --> H[OpenAI Service]
  E --> I[Stripe Service]
  E --> J[Email Service]

  subgraph "Express Server"
    B
    C
    D
    E
    F
  end

  subgraph "External APIs"
    H
    I
    J
  end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
  USER ||--o{ GENERATION : creates
  USER ||--o{ SUBSCRIPTION : has
  USER ||--o{ USAGE_LOG : generates
  GENERATION ||--o{ GENERATION_FEEDBACK : receives
  SUBSCRIPTION ||--o{ INVOICE : generates

  USER {
    string id PK
    string email
    string name
    string provider
    string providerId
    number creditsRemaining
    string plan
    datetime createdAt
    datetime updatedAt
  }

  GENERATION {
    string id PK
    string userId FK
    string prompt
    string generatedCode
    string status
    number tokensUsed
    datetime createdAt
    boolean isFavorite
  }

  SUBSCRIPTION {
    string id PK
    string userId FK
    string stripeSubscriptionId
    string plan
    string status
    datetime currentPeriodStart
    datetime currentPeriodEnd
    number creditsIncluded
  }

  USAGE_LOG {
    string id PK
    string userId FK
    string action
    string metadata
    datetime timestamp
  }

  GENERATION_FEEDBACK {
    string id PK
    string generationId FK
    number rating
    string feedback
    datetime createdAt
  }

  INVOICE {
    string id PK
    string subscriptionId FK
    string stripeInvoiceId
    number amount
    string status
    datetime createdAt
  }
```

### 6.2 Data Definition Language

**NextAuth Configuration**

```javascript
// NextAuth.js Configuration
export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    secret: process.env.NEXTAUTH_SECRET,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (account && user) {
        token.accessToken = account.access_token
        token.provider = account.provider
        token.userId = user.id
      }
      return token
    },
    async session({ session, token }) {
      session.accessToken = token.accessToken
      session.user.id = token.userId
      session.user.provider = token.provider
      return session
    }
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error'
  }
}
```

**Users Collection**

```javascript
// MongoDB Collection: users
{
  _id: ObjectId,
  email: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  image: { type: String }, // Profile image from OAuth provider
  provider: { type: String, default: 'google' },
  providerId: { type: String, required: true },
  creditsRemaining: { type: Number, default: 5 },
  plan: { type: String, enum: ['free', 'premium', 'enterprise'], default: 'free' },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  emailVerified: { type: Date }, // NextAuth email verification
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  lastLoginAt: { type: Date },
  preferences: {
    codeStyle: { type: String, default: 'standard' },
    includeComments: { type: Boolean, default: true },
    emailNotifications: { type: Boolean, default: true }
  }
}

// Indexes
db.users.createIndex({ "email": 1 }, { unique: true })
db.users.createIndex({ "providerId": 1 })
db.users.createIndex({ "createdAt": -1 })
db.users.createIndex({ "provider": 1, "providerId": 1 }, { unique: true })
```

**Generations Collection**

```javascript
// MongoDB Collection: generations
{
  _id: ObjectId,
  userId: { type: ObjectId, ref: 'User', required: true },
  prompt: { type: String, required: true, maxLength: 500 },
  generatedCode: { type: String, required: true },
  status: { type: String, enum: ['success', 'error', 'pending'], default: 'pending' },
  tokensUsed: { type: Number, default: 0 },
  executionTime: { type: Number }, // milliseconds
  createdAt: { type: Date, default: Date.now },
  isFavorite: { type: Boolean, default: false },
  tags: [{ type: String }],
  metadata: {
    model: { type: String, default: 'gpt-4' }, // Admin-controlled
    temperature: { type: Number, default: 0.1 }, // Admin-controlled
    maxTokens: { type: Number, default: 1000 } // Admin-controlled
  }
}

// Indexes
db.generations.createIndex({ "userId": 1, "createdAt": -1 })
db.generations.createIndex({ "userId": 1, "isFavorite": 1 })
db.generations.createIndex({ "prompt": "text", "generatedCode": "text" })
```

**Subscriptions Collection**

```javascript
// MongoDB Collection: subscriptions
{
  _id: ObjectId,
  userId: { type: ObjectId, ref: 'User', required: true },
  stripeSubscriptionId: { type: String, required: true, unique: true },
  plan: { type: String, enum: ['premium_monthly', 'premium_annual'], required: true },
  status: { type: String, enum: ['active', 'canceled', 'past_due'], required: true },
  currentPeriodStart: { type: Date, required: true },
  currentPeriodEnd: { type: Date, required: true },
  creditsIncluded: { type: Number, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}

// Indexes
db.subscriptions.createIndex({ "userId": 1 })
db.subscriptions.createIndex({ "stripeSubscriptionId": 1 }, { unique: true })
db.subscriptions.createIndex({ "status": 1, "currentPeriodEnd": 1 })
```

**Usage Logs Collection**

```javascript
// MongoDB Collection: usage_logs
{
  _id: ObjectId,
  userId: { type: ObjectId, ref: 'User', required: true },
  action: { type: String, enum: ['generation', 'login', 'subscription_change'], required: true },
  metadata: { type: Object },
  timestamp: { type: Date, default: Date.now },
  ipAddress: { type: String },
  userAgent: { type: String }
}

// Indexes
db.usage_logs.createIndex({ "userId": 1, "timestamp": -1 })
db.usage_logs.createIndex({ "action": 1, "timestamp": -1 })
db.usage_logs.createIndex({ "timestamp": -1 }, { expireAfterSeconds: 7776000 }) // 90 days TTL
```

**NextAuth Accounts Collection**

```javascript
// MongoDB Collection: accounts (NextAuth)
{
  _id: ObjectId,
  userId: { type: ObjectId, ref: 'User', required: true },
  type: { type: String, required: true }, // 'oauth'
  provider: { type: String, required: true }, // 'google'
  providerAccountId: { type: String, required: true },
  refresh_token: { type: String },
  access_token: { type: String },
  expires_at: { type: Number },
  token_type: { type: String },
  scope: { type: String },
  id_token: { type: String },
  session_state: { type: String },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}

// Indexes
db.accounts.createIndex({ "userId": 1 })
db.accounts.createIndex({ "provider": 1, "providerAccountId": 1 }, { unique: true })
```

**NextAuth Sessions Collection**

```javascript
// MongoDB Collection: sessions (NextAuth)
{
  _id: ObjectId,
  sessionToken: { type: String, required: true, unique: true },
  userId: { type: ObjectId, ref: 'User', required: true },
  expires: { type: Date, required: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}

// Indexes
db.sessions.createIndex({ "sessionToken": 1 }, { unique: true })
db.sessions.createIndex({ "userId": 1 })
db.sessions.createIndex({ "expires": 1 }, { expireAfterSeconds: 0 })
```

**Environment Variables**

```bash
# NextAuth Configuration
NEXTAUTH_URL=https://generatevba.codes
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_SIGNING_KEY=your-jwt-signing-key

# Security
CSRF_SECRET=your-csrf-secret
```

**Initial Data Setup**

```javascript
// Default user plans configuration
db.plans.insertMany([
  {
    _id: "free",
    name: "Free Plan",
    creditsPerMonth: 5,
    price: 0,
    features: ["Basic VBA generation", "5 credits/month", "Community support"]
  },
  {
    _id: "premium_monthly",
    name: "Premium Monthly",
    creditsPerMonth: 100,
    price: 1999, // $19.99 in cents
    features: ["Unlimited VBA generation", "Priority support", "Advanced features", "Export to .bas files"]
  },
  {
    _id: "premium_annual",
    name: "Premium Annual",
    creditsPerMonth: 100,
    price: 19999, // $199.99 in cents
    features: ["Unlimited VBA generation", "Priority support", "Advanced features", "Export to .bas files", "2 months free"]
  }
])
```

