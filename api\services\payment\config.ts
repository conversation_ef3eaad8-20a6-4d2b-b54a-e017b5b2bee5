import { PaymentConfig, PaymentPlan } from './types.js';

export class PaymentConfigService {
  private static instance: PaymentConfigService;
  private config: PaymentConfig;

  private constructor() {
    this.config = this.loadConfig();
  }

  public static getInstance(): PaymentConfigService {
    if (!PaymentConfigService.instance) {
      PaymentConfigService.instance = new PaymentConfigService();
    }
    return PaymentConfigService.instance;
  }

  private loadConfig(): PaymentConfig {
    const provider = process.env.PAYMENT_PROVIDER as 'stripe' | 'dodopayments';
    
    if (!provider || !['stripe', 'dodopayments'].includes(provider)) {
      throw new Error('PAYMENT_PROVIDER must be set to either "stripe" or "dodopayments"');
    }

    const freeCredits = parseInt(process.env.FREE_PLAN_CREDITS || '5');
    const proPrice = parseFloat(process.env.PRO_PLAN_PRICE || '4.99');
    const proCurrency = process.env.PRO_PLAN_CURRENCY || 'USD';
    const proCredits = process.env.PRO_PLAN_CREDITS === 'unlimited' ? 'unlimited' : parseInt(process.env.PRO_PLAN_CREDITS || '0');
    const enterpriseCredits = process.env.ENTERPRISE_PLAN_CREDITS === 'unlimited' ? 'unlimited' : parseInt(process.env.ENTERPRISE_PLAN_CREDITS || '0');

    return {
      provider,
      plans: {
        free: {
          credits: freeCredits
        },
        pro: {
          price: proPrice,
          currency: proCurrency,
          credits: proCredits
        },
        enterprise: {
          credits: enterpriseCredits
        }
      }
    };
  }

  public getProvider(): 'stripe' | 'dodopayments' {
    return this.config.provider;
  }

  public getPlans(): PaymentPlan[] {
    return [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: this.config.plans.pro.currency,
        credits: this.config.plans.free.credits,
        features: [
          `${this.config.plans.free.credits} code generations`,
          'Basic VBA templates',
          'Community support'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        price: this.config.plans.pro.price,
        currency: this.config.plans.pro.currency,
        credits: this.config.plans.pro.credits,
        interval: 'month',
        features: [
          this.config.plans.pro.credits === 'unlimited' ? 'Unlimited code generations' : `${this.config.plans.pro.credits} code generations per month`,
          'Advanced VBA templates',
          'Priority support',
          'Code export options',
          'Custom templates'
        ]
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: 0, // Manually assigned, no public pricing
        currency: this.config.plans.pro.currency,
        credits: this.config.plans.enterprise.credits,
        features: [
          this.config.plans.enterprise.credits === 'unlimited' ? 'Unlimited code generations' : `${this.config.plans.enterprise.credits} code generations`,
          'All VBA templates',
          'Dedicated support',
          'API access',
          'Team collaboration',
          'Custom integrations'
        ]
      }
    ];
  }

  public getPlan(planId: string): PaymentPlan | undefined {
    return this.getPlans().find(plan => plan.id === planId);
  }

  public getConfig(): PaymentConfig {
    return { ...this.config };
  }

  public getCreditsForPlan(planId: string): number | 'unlimited' {
    const plan = this.getPlan(planId);
    return plan ? plan.credits : 0;
  }
}

export const paymentConfig = PaymentConfigService.getInstance();