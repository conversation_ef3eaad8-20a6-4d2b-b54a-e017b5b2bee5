import express, { Router, type Request, type Response } from 'express';
import User from '../models/User.js';
import { authenticateSession, AuthRequest } from '../utils/nextauth-helpers.js';
import { getPaymentProvider, paymentConfig } from '../services/payment/index.js';

const router = Router();

/**
 * Get subscription plans
 * GET /api/billing/plans
 */
router.get('/plans', async (req: Request, res: Response): Promise<void> => {
  try {
    const paymentProvider = getPaymentProvider();
    const plans = await paymentProvider.getPlans();

    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription plans'
    });
  }
});

/**
 * Create checkout session
 * POST /api/billing/create-checkout-session
 */
router.post('/create-checkout-session', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { planId } = req.body;

    if (!planId || planId === 'free') {
      res.status(400).json({
        success: false,
        error: 'Invalid plan selected'
      });
      return;
    }

    // Validate plan exists
    const plan = paymentConfig.getPlan(planId);
    if (!plan) {
      res.status(400).json({
        success: false,
        error: 'Invalid plan selected'
      });
      return;
    }

    const user = await User.findById(req.user!.userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    const paymentProvider = getPaymentProvider();

    // Create or get payment customer
    let customerId = user.subscription.paymentCustomerId;
    if (!customerId) {
      const customer = await paymentProvider.createCustomer({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      });
      customerId = customer.id;
      
      // Update user with customer ID
      await User.findByIdAndUpdate(user._id, {
        'subscription.paymentCustomerId': customerId
      });
    }

    // Create checkout session
    const session = await paymentProvider.createCheckoutSession({
      customerId,
      planId,
      successUrl: `${process.env.NEXTAUTH_URL}/billing?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${process.env.NEXTAUTH_URL}/billing?canceled=true`,
      metadata: {
        userId: user._id.toString(),
        planId
      }
    });

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url
      }
    });
  } catch (error) {
    console.error('Create checkout session error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create checkout session'
    });
  }
});

/**
 * Get user's subscription status
 * GET /api/billing/subscription
 */
router.get('/subscription', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user!.userId).select('subscription credits');
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: {
        subscription: user.subscription,
        credits: user.credits
      }
    });
  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription status'
    });
  }
});

/**
 * Cancel subscription
 * POST /api/billing/cancel-subscription
 */
router.post('/cancel-subscription', authenticateSession, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user!.userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    if (!user.subscription.paymentSubscriptionId) {
      res.status(400).json({
        success: false,
        error: 'No active subscription found'
      });
      return;
    }

    const paymentProvider = getPaymentProvider();

    // Cancel subscription at period end
    await paymentProvider.cancelSubscription(user.subscription.paymentSubscriptionId, true);

    // Update user subscription status
    await User.findByIdAndUpdate(user._id, {
      'subscription.status': 'canceled'
    });

    res.json({
      success: true,
      message: 'Subscription will be canceled at the end of the current period'
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription'
    });
  }
});

/**
 * Payment webhook handler
 * POST /api/billing/webhook
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req: Request, res: Response): Promise<void> => {
  const paymentProvider = getPaymentProvider();
  const providerType = paymentConfig.getProvider();
  
  // Get signature header based on provider
  const signature = providerType === 'stripe' 
    ? req.headers['stripe-signature'] as string
    : req.headers['x-dodopayments-signature'] as string;

  if (!signature) {
    console.error('Webhook signature header missing');
    res.status(400).send('Webhook signature header missing');
    return;
  }

  let event;

  try {
    event = await paymentProvider.constructWebhookEvent(req.body, signature, '');
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    res.status(400).send('Webhook signature verification failed');
    return;
  }

  try {
    await handleWebhookEvent(event, paymentProvider);
    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

/**
 * Handle webhook events from payment providers
 */
async function handleWebhookEvent(event: any, paymentProvider: any): Promise<void> {
  const eventType = normalizeEventType(event.type);
  
  switch (eventType) {
    case 'checkout_completed': {
      const sessionData = event.data.object || event.data;
      const userId = sessionData.metadata?.userId;
      const planId = sessionData.metadata?.planId;

      if (userId && planId) {
        const plan = paymentConfig.getPlan(planId);
        if (!plan) {
          console.error(`Invalid plan ID in webhook: ${planId}`);
          return;
        }

        // For subscription plans, get subscription details
        let subscriptionId = null;
        let currentPeriodEnd = null;
        
        if (planId === 'pro') {
          // Extract subscription ID based on provider
          if (paymentConfig.getProvider() === 'stripe') {
            subscriptionId = sessionData.subscription;
            if (subscriptionId) {
              const subscription = await paymentProvider.getSubscription(subscriptionId);
              currentPeriodEnd = subscription.currentPeriodEnd;
            }
          } else {
            subscriptionId = sessionData.subscription_id;
            if (subscriptionId) {
              const subscription = await paymentProvider.getSubscription(subscriptionId);
              currentPeriodEnd = subscription.currentPeriodEnd;
            }
          }
        }

        await User.findByIdAndUpdate(userId, {
          'subscription.plan': planId,
          'subscription.status': 'active',
          'subscription.paymentSubscriptionId': subscriptionId,
          'subscription.currentPeriodEnd': currentPeriodEnd,
          'credits.total': plan.credits === 'unlimited' ? -1 : plan.credits,
          'credits.remaining': plan.credits === 'unlimited' ? -1 : plan.credits,
          'credits.used': 0
        });
      }
      break;
    }

    case 'payment_succeeded': {
      const invoiceData = event.data.object || event.data;
      const subscriptionId = invoiceData.subscription || invoiceData.subscription_id;
      
      if (subscriptionId) {
        const user = await User.findOne({ 'subscription.paymentSubscriptionId': subscriptionId });
        
        if (user) {
          const plan = paymentConfig.getPlan(user.subscription.plan);
          if (plan) {
            const subscription = await paymentProvider.getSubscription(subscriptionId);
            
            await User.findByIdAndUpdate(user._id, {
              'subscription.status': 'active',
              'subscription.currentPeriodEnd': subscription.currentPeriodEnd,
              'credits.total': plan.credits === 'unlimited' ? -1 : plan.credits,
              'credits.remaining': plan.credits === 'unlimited' ? -1 : plan.credits,
              'credits.used': 0
            });
          }
        }
      }
      break;
    }

    case 'payment_failed': {
      const invoiceData = event.data.object || event.data;
      const subscriptionId = invoiceData.subscription || invoiceData.subscription_id;
      
      if (subscriptionId) {
        await User.findOneAndUpdate(
          { 'subscription.paymentSubscriptionId': subscriptionId },
          { 'subscription.status': 'past_due' }
        );
      }
      break;
    }

    case 'subscription_deleted': {
      const subscriptionData = event.data.object || event.data;
      const subscriptionId = subscriptionData.id;
      
      const freePlan = paymentConfig.getPlan('free');
      if (freePlan) {
        await User.findOneAndUpdate(
          { 'subscription.paymentSubscriptionId': subscriptionId },
          {
            'subscription.plan': 'free',
            'subscription.status': 'canceled',
            'subscription.paymentSubscriptionId': null,
            'subscription.currentPeriodEnd': null,
            'credits.total': freePlan.credits,
            'credits.remaining': freePlan.credits,
            'credits.used': 0
          }
        );
      }
      break;
    }

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }
}

/**
 * Normalize event types from different providers to common names
 */
function normalizeEventType(eventType: string): string {
  // Stripe event types
  if (eventType === 'checkout.session.completed') return 'checkout_completed';
  if (eventType === 'invoice.payment_succeeded') return 'payment_succeeded';
  if (eventType === 'invoice.payment_failed') return 'payment_failed';
  if (eventType === 'customer.subscription.deleted') return 'subscription_deleted';
  
  // DodoPayments event types (normalize to common format)
  if (eventType === 'checkout_session.completed') return 'checkout_completed';
  if (eventType === 'subscription.payment_succeeded') return 'payment_succeeded';
  if (eventType === 'subscription.payment_failed') return 'payment_failed';
  if (eventType === 'subscription.deleted') return 'subscription_deleted';
  
  return eventType;
}

export default router;