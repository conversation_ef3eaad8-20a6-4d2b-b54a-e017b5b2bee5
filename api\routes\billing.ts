import express, { Router, type Request, type Response } from 'express';
import <PERSON><PERSON> from 'stripe';
import User from '../models/User.js';
import { authenticateToken } from '../middleware/auth.js';

const router = Router();

interface AuthRequest extends Request {
  user?: {
    userId: string;
    email: string;
  };
}

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-07-30.basil'
});

/**
 * Get subscription plans
 * GET /api/billing/plans
 */
router.get('/plans', async (req: Request, res: Response): Promise<void> => {
  try {
    const plans = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        credits: 10,
        features: [
          '10 code generations per month',
          'Basic VBA templates',
          'Community support'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        price: 9.99,
        credits: 100,
        features: [
          '100 code generations per month',
          'Advanced VBA templates',
          'Priority support',
          'Code export options',
          'Custom templates'
        ]
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: 29.99,
        credits: 500,
        features: [
          '500 code generations per month',
          'All VBA templates',
          'Dedicated support',
          'API access',
          'Team collaboration',
          'Custom integrations'
        ]
      }
    ];

    res.json({
      success: true,
      data: plans
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription plans'
    });
  }
});

/**
 * Create checkout session
 * POST /api/billing/create-checkout-session
 */
router.post('/create-checkout-session', authenticateToken, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { planId } = req.body;

    if (!planId || planId === 'free') {
      res.status(400).json({
        success: false,
        error: 'Invalid plan selected'
      });
      return;
    }

    const user = await User.findById(req.user!.userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    // Define price IDs (these should be created in Stripe dashboard)
    const priceIds = {
      pro: process.env.STRIPE_PRO_PRICE_ID || 'price_pro',
      enterprise: process.env.STRIPE_ENTERPRISE_PRICE_ID || 'price_enterprise'
    };

    const priceId = priceIds[planId as keyof typeof priceIds];
    if (!priceId) {
      res.status(400).json({
        success: false,
        error: 'Invalid plan selected'
      });
      return;
    }

    // Create or get Stripe customer
    let customerId = user.subscription.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      });
      customerId = customer.id;
      
      // Update user with customer ID
      await User.findByIdAndUpdate(user._id, {
        'subscription.stripeCustomerId': customerId
      });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1
        }
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXTAUTH_URL}/billing?success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXTAUTH_URL}/billing?canceled=true`,
      metadata: {
        userId: user._id.toString(),
        planId
      }
    });

    res.json({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url
      }
    });
  } catch (error) {
    console.error('Create checkout session error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create checkout session'
    });
  }
});

/**
 * Get user's subscription status
 * GET /api/billing/subscription
 */
router.get('/subscription', authenticateToken, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user!.userId).select('subscription credits');
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    res.json({
      success: true,
      data: {
        subscription: user.subscription,
        credits: user.credits
      }
    });
  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get subscription status'
    });
  }
});

/**
 * Cancel subscription
 * POST /api/billing/cancel-subscription
 */
router.post('/cancel-subscription', authenticateToken, async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const user = await User.findById(req.user!.userId);
    if (!user) {
      res.status(404).json({
        success: false,
        error: 'User not found'
      });
      return;
    }

    if (!user.subscription.stripeSubscriptionId) {
      res.status(400).json({
        success: false,
        error: 'No active subscription found'
      });
      return;
    }

    // Cancel subscription at period end
    await stripe.subscriptions.update(user.subscription.stripeSubscriptionId, {
      cancel_at_period_end: true
    });

    // Update user subscription status
    await User.findByIdAndUpdate(user._id, {
      'subscription.status': 'canceled'
    });

    res.json({
      success: true,
      message: 'Subscription will be canceled at the end of the current period'
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cancel subscription'
    });
  }
});

/**
 * Stripe webhook handler
 * POST /api/billing/webhook
 */
router.post('/webhook', express.raw({ type: 'application/json' }), async (req: Request, res: Response): Promise<void> => {
  const sig = req.headers['stripe-signature'] as string;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  if (!webhookSecret) {
    console.error('Stripe webhook secret not configured');
    res.status(500).send('Webhook secret not configured');
    return;
  }

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    res.status(400).send('Webhook signature verification failed');
    return;
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const userId = session.metadata?.userId;
        const planId = session.metadata?.planId;

        if (userId && planId) {
          // Get subscription details
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string);
          
          // Update user subscription
          const creditsMap = { pro: 100, enterprise: 500 };
          const credits = creditsMap[planId as keyof typeof creditsMap] || 10;

          await User.findByIdAndUpdate(userId, {
            'subscription.plan': planId,
            'subscription.status': 'active',
            'subscription.stripeSubscriptionId': subscription.id,
            'subscription.currentPeriodEnd': new Date((subscription as any).current_period_end * 1000),
            'credits.total': credits,
            'credits.remaining': credits,
            'credits.used': 0
          });
        }
        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        const subscriptionId = (invoice as any).subscription as string;
        
        if (subscriptionId) {
          const subscription = await stripe.subscriptions.retrieve(subscriptionId);
          const user = await User.findOne({ 'subscription.stripeSubscriptionId': subscriptionId });
          
          if (user) {
            // Reset credits for new billing period
            const creditsMap = { pro: 100, enterprise: 500 };
            const credits = creditsMap[user.subscription.plan as keyof typeof creditsMap] || 10;
            
            await User.findByIdAndUpdate(user._id, {
              'subscription.status': 'active',
              'subscription.currentPeriodEnd': new Date((subscription as any).current_period_end * 1000),
              'credits.total': credits,
              'credits.remaining': credits,
              'credits.used': 0
            });
          }
        }
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        const subscriptionId = (invoice as any).subscription as string;
        
        if (subscriptionId) {
          await User.findOneAndUpdate(
            { 'subscription.stripeSubscriptionId': subscriptionId },
            { 'subscription.status': 'past_due' }
          );
        }
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        
        await User.findOneAndUpdate(
          { 'subscription.stripeSubscriptionId': subscription.id },
          {
            'subscription.plan': 'free',
            'subscription.status': 'canceled',
            'subscription.stripeSubscriptionId': null,
            'subscription.currentPeriodEnd': null,
            'credits.total': 10,
            'credits.remaining': 10,
            'credits.used': 0
          }
        );
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

export default router;