{"name": "vba2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"client:dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "check": "tsc --noEmit", "server:dev": "nodemon", "dev": "concurrently \"npm run client:dev\" \"npm run server:dev\""}, "dependencies": {"@next-auth/mongodb-adapter": "^1.1.3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "google-auth-library": "^9.14.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^5.9.2", "mongoose": "^8.8.4", "next-auth": "^4.24.11", "openai": "^5.12.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "stripe": "^18.4.0", "tailwind-merge": "^3.0.2", "winston": "^3.17.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/node": "^22.15.30", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vercel/node": "^5.3.6", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-plugin-react-dev-locator": "^1.0.0", "concurrently": "^9.2.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.1.10", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vite-tsconfig-paths": "^5.1.4"}}