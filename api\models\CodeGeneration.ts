import mongoose, { Document, Schema } from 'mongoose';

export interface ICodeGeneration extends Document {
  _id: string;
  userId: string;
  prompt: string;
  generatedCode: string;
  language: 'vba' | 'excel-vba' | 'word-vba' | 'access-vba';
  status: 'pending' | 'completed' | 'failed';
  metadata: {
    tokensUsed: number;
    executionTime: number;
    model: string;
  };
  tags: string[];
  isPublic: boolean;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
}

const CodeGenerationSchema = new Schema<ICodeGeneration>({
  userId: {
    type: String,
    required: true,
    ref: 'User'
  },
  prompt: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  generatedCode: {
    type: String,
    required: false,
    default: ''
  },
  language: {
    type: String,
    enum: ['vba', 'excel-vba', 'word-vba', 'access-vba'],
    default: 'vba'
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed'],
    default: 'pending'
  },
  metadata: {
    tokensUsed: {
      type: Number,
      default: 0
    },
    executionTime: {
      type: Number,
      default: 0
    },
    model: {
      type: String,
      default: 'gpt-3.5-turbo'
    }
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  likes: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes
CodeGenerationSchema.index({ userId: 1, createdAt: -1 });
CodeGenerationSchema.index({ language: 1 });
CodeGenerationSchema.index({ tags: 1 });
CodeGenerationSchema.index({ isPublic: 1, likes: -1 });

export default mongoose.model<ICodeGeneration>('CodeGeneration', CodeGenerationSchema);