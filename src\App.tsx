import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SessionProvider from "@/components/SessionProvider";
import Home from "@/pages/Home";
import SignIn from "@/pages/SignIn";
import SignUp from "@/pages/SignUp";
import Dashboard from "@/pages/Dashboard";
import Generate from "@/pages/Generate";
import History from "@/pages/History";
import Billing from "@/pages/Billing";
import Profile from "@/pages/Profile";
import Admin from "@/pages/Admin";

export default function App() {
  return (
    <SessionProvider>
      <Router>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/signin" element={<SignIn />} />
          <Route path="/signup" element={<SignUp />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/generate" element={<Generate />} />
          <Route path="/history" element={<History />} />
          <Route path="/billing" element={<Billing />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/admin" element={<Admin />} />
        </Routes>
      </Router>
    </SessionProvider>
  );
}
