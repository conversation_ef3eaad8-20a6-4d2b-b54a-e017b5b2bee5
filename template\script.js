// Minimal JS for small UI niceties (no external deps)

// Subtle hover ripple on solid CTA
(function () {
  const cta = document.querySelector('.cta.solid');
  if (!cta) return;

  cta.addEventListener('mousemove', (e) => {
    const rect = cta.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    cta.style.setProperty('--mx', `${x}px`);
    cta.style.setProperty('--my', `${y}px`);
  });

  // Add radial highlight using CSS paint vars
  const style = document.createElement('style');
  style.textContent = `
    .cta.solid{
      position:relative;
      overflow:hidden;
      isolation:isolate;
    }
    .cta.solid::before{
      content:"";
      position:absolute; inset:-1px;
      background: radial-gradient(180px 180px at var(--mx,50%) var(--my,50%), rgba(255,255,255,.25), transparent 60%);
      pointer-events:none;
      transition: opacity .2s ease;
      opacity:0;
      z-index:0;
      border-radius:inherit;
    }
    .cta.solid:hover::before{opacity:1}
    .cta.solid > *{position:relative; z-index:1}
  `;
  document.head.appendChild(style);
})();

// Smooth scroll for local links (nav)
(function () {
  const links = document.querySelectorAll('a[href^="#"]');
  links.forEach((a) => {
    a.addEventListener('click', (e) => {
      const id = a.getAttribute('href');
      if (!id || id === '#') return;
      const el = document.querySelector(id);
      if (!el) return;
      e.preventDefault();
      window.scrollTo({
        top: el.getBoundingClientRect().top + window.scrollY - 70,
        behavior: 'smooth'
      });
    });
  });
})();
