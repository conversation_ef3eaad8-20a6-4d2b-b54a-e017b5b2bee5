import express from 'express';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';

const router = express.Router();

// Register new user
router.post('/', async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({ error: 'Name, email, and password are required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({ error: 'Password must be at least 6 characters long' });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }

    // Hash password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create new user
    const newUser = new User({
      name,
      email: email.toLowerCase(),
      password: hashedPassword,
      provider: 'email',
      subscription: {
        plan: 'free',
        status: 'active',
        startDate: new Date(),
      },
      credits: {
        total: 10,
        used: 0,
        remaining: 10,
      },
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: true,
      },
    });

    await newUser.save();

    // Return success (don't include password in response)
    const userResponse = {
      id: newUser._id,
      name: newUser.name,
      email: newUser.email,
      provider: newUser.provider,
      subscription: newUser.subscription,
      credits: newUser.credits,
      preferences: newUser.preferences,
    };

    res.status(201).json({
      message: 'User created successfully',
      user: userResponse,
    });
  