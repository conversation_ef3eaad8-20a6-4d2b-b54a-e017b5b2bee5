import { getServerSession } from 'next-auth/next'
import { authOptions } from '../auth/[...nextauth].js'
import { Request, Response, NextFunction } from 'express'
import { IncomingMessage, ServerResponse } from 'http'

// Extend Express Request to include NextAuth session
export interface AuthRequest extends Request {
  user?: {
    userId: string
    email: string
    name: string
    provider: 'google' | 'email'
    subscription: {
      plan: 'free' | 'pro' | 'enterprise'
      status: 'active' | 'canceled' | 'past_due'
      stripeCustomerId?: string
      stripeSubscriptionId?: string
      currentPeriodEnd?: Date
    }
    credits: {
      total: number
      used: number
      remaining: number
    }
    preferences: {
      theme: 'light' | 'dark'
      language: string
      notifications: boolean
    }
  }
}

/**
 * NextAuth session middleware for Express routes
 * Replaces the custom JWT authenticateToken middleware
 */
export async function authenticateSession(
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    // Convert Express req/res to format expected by NextAuth
    const nextAuthReq = {
      ...req,
      cookies: req.cookies || {}
    } as any
    const nextAuthRes = res as unknown as ServerResponse
    
    const session = await getServerSession(nextAuthReq, nextAuthRes, authOptions)
    
    if (!session || !session.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      })
      return
    }
    
    // Attach user data to request
    req.user = {
      userId: session.user.id,
      email: session.user.email!,
      name: session.user.name!,
      provider: session.user.provider,
      subscription: session.user.subscription,
      credits: session.user.credits,
      preferences: session.user.preferences
    }
    
    next()
  } catch (error) {
    console.error('Session authentication error:', error)
    res.status(401).json({
      success: false,
      error: 'Invalid session'
    })
  }
}

/**
 * Check if user has sufficient credits
 * Updated to work with NextAuth session
 */
export async function checkCredits(
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      })
      return
    }
    
    if (req.user.credits.remaining <= 0) {
      res.status(403).json({
        success: false,
        error: 'Insufficient credits. Please upgrade your plan or purchase more credits.'
      })
      return
    }
    
    next()
  } catch (error) {
    console.error('Credits check error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to check credits'
    })
  }
}

/**
 * Check if user is admin
 * Updated to work with NextAuth session
 */
export async function requireAdmin(
  req: AuthRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      })
      return
    }
    
    const adminEmails = process.env.ADMIN_EMAILS?.split(',').map(email => email.trim()) || []
    
    if (!adminEmails.includes(req.user.email)) {
      res.status(403).json({
        success: false,
        error: 'Admin access required'
      })
      return
    }
    
    next()
  } catch (error) {
    console.error('Admin check error:', error)
    res.status(500).json({
      success: false,
      error: 'Failed to verify admin status'
    })
  }
}