import { useState, useEffect } from 'react';
import { Link, useNavigate } from "react-router-dom";
import { useAuthStore } from '../stores/authStore';
import { Code, Clock, CreditCard, TrendingUp, Zap, History, LogOut } from 'lucide-react';

interface UserStats {
  totalGenerations: number;
  creditsRemaining: number;
  currentPlan: string;
}

interface RecentGeneration {
  _id: string;
  prompt: string;
  createdAt: string;
  language: string;
}

export default function Dashboard() {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [recentGenerations, setRecentGenerations] = useState<RecentGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, token, logout } = useAuthStore();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!user || !token) {
      navigate('/auth/signin');
      return;
    }
    
    fetchDashboardData();
  }, [user, token, navigate]);
  
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch user stats
      const userResponse = await fetch('/api/users/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (userResponse.ok) {
        const userData = await userResponse.json();
        setStats({
          totalGenerations: userData.totalGenerations || 0,
          creditsRemaining: userData.credits || 0,
          currentPlan: userData.subscription?.plan || 'Free',
        });
      }
      
      // Fetch recent generations
      const historyResponse = await fetch('/api/code/history?limit=3', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setRecentGenerations(historyData.generations || []);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const handleLogout = () => {
    logout();
    navigate('/');
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }
  };
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="brand">
                <span className="brand-logo">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                    <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                    <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
                  </svg>
                </span>
                <span className="brand-text">GenerateVBA</span>
              </Link>
            </div>
            <nav className="flex items-center space-x-4">
              <Link to="/generate" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <Code className="h-4 w-4 mr-1" />
                Generate
              </Link>
              <Link to="/history" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <History className="h-4 w-4 mr-1" />
                History
              </Link>
              <Link to="/billing" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <CreditCard className="h-4 w-4 mr-1" />
                Billing
              </Link>
              <Link to="/profile" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium">
                Profile
              </Link>
              <div className="flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200">
                <span className="text-sm text-gray-600">Hi, {user?.name || 'User'}</span>
                <button
                  onClick={handleLogout}
                  className="text-gray-700 hover:text-red-600 p-2 rounded-md transition-colors"
                  title="Logout"
                >
                  <LogOut className="h-4 w-4" />
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Welcome back, {user?.name || 'User'}!</h1>
            <p className="mt-2 text-gray-600">Ready to generate some VBA code? Let's automate your Excel tasks.</p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="card text-center">
              <div className="flex items-center justify-center mb-3">
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {loading ? '...' : stats?.totalGenerations || 0}
              </div>
              <div className="text-gray-600">VBA Scripts Generated</div>
            </div>
            <div className="card text-center">
              <div className="flex items-center justify-center mb-3">
                <Zap className="h-8 w-8 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-green-600 mb-2">
                {loading ? '...' : stats?.creditsRemaining || 0}
              </div>
              <div className="text-gray-600">Credits Remaining</div>
            </div>
            <div className="card text-center">
              <div className="flex items-center justify-center mb-3">
                <CreditCard className="h-8 w-8 text-blue-600" />
              </div>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {loading ? '...' : stats?.currentPlan || 'Free'}
              </div>
              <div className="text-gray-600">Current Plan</div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div className="card">
              <h3 className="text-xl font-bold mb-4">🚀 Generate New VBA Code</h3>
              <p className="text-gray-600 mb-4">
                Describe what you want your macro to do, and our AI will create the VBA code for you.
              </p>
              <Link to="/generate" className="cta solid">
                Start Generating
              </Link>
            </div>
            
            <div className="card">
              <h3 className="text-xl font-bold mb-4">📚 Browse Your History</h3>
              <p className="text-gray-600 mb-4">
                View, copy, and reuse your previously generated VBA scripts.
              </p>
              <Link to="/history" className="cta ghost">
                View History
              </Link>
            </div>
          </div>

          {/* Recent Generations */}
          <div className="card">
            <div className="flex items-center mb-4">
              <Clock className="h-5 w-5 text-gray-500 mr-2" />
              <h3 className="text-xl font-bold">Recent Generations</h3>
            </div>
            
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="border-l-4 border-gray-300 pl-4 py-2 animate-pulse">
                    <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/4 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-full"></div>
                  </div>
                ))}
              </div>
            ) : recentGenerations.length > 0 ? (
              <div className="space-y-4">
                {recentGenerations.map((generation, index) => {
                  const borderColors = ['border-purple-500', 'border-blue-500', 'border-green-500'];
                  return (
                    <div key={generation._id} className={`border-l-4 ${borderColors[index]} pl-4 py-2`}>
                      <h4 className="font-semibold text-gray-900">
                        {generation.prompt.length > 50 
                          ? `${generation.prompt.substring(0, 50)}...` 
                          : generation.prompt
                        }
                      </h4>
                      <p className="text-gray-600 text-sm flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDate(generation.createdAt)}
                      </p>
                      <p className="text-gray-700 mt-1">
                        {generation.language} code generation
                      </p>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Code className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No generations yet</p>
                <p className="text-sm">Start by creating your first VBA script!</p>
              </div>
            )}
            
            <div className="mt-4 text-center">
              <Link to="/history" className="text-purple-600 hover:text-purple-500 font-medium flex items-center justify-center">
                View all generations
                <History className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>

          {/* Upgrade Banner */}
          <div className="mt-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-bold mb-2">Upgrade to Premium</h3>
                <p className="text-purple-100">
                  Get unlimited generations, advanced features, and priority support.
                </p>
              </div>
              <Link to="/billing" className="bg-white text-purple-600 px-6 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                Upgrade Now
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}