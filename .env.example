# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# MongoDB
MONGODB_URI=mongodb://localhost:27017/vba-generator

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Payment Provider Configuration
PAYMENT_PROVIDER=stripe

# Plan Configuration
FREE_PLAN_CREDITS=5
PRO_PLAN_PRICE=4.99
PRO_PLAN_CURRENCY=USD
PRO_PLAN_CREDITS=unlimited
ENTERPRISE_PLAN_CREDITS=unlimited

# Stripe Configuration
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
STRIPE_PRO_PRICE_ID=your-stripe-pro-price-id
STRIPE_API_VERSION=2025-03-31.basil

# DodoPayments Configuration
DODOPAYMENTS_API_KEY=your-dodopayments-api-key
DODOPAYMENTS_SECRET_KEY=your-dodopayments-secret-key
DODOPAYMENTS_WEBHOOK_SECRET=your-dodopayments-webhook-secret
DODOPAYMENTS_PRO_PLAN_ID=your-dodopayments-pro-plan-id
DODOPAYMENTS_ENVIRONMENT=sandbox

# Server
PORT=3001

# Admin
ADMIN_EMAILS=<EMAIL>