import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  role?: 'user' | 'admin';
  subscription: {
    plan: 'free' | 'pro' | 'enterprise';
    status: 'active' | 'canceled' | 'past_due';
    currentPeriodEnd?: string;
  };
  credits: {
    total: number;
    used: number;
    remaining: number;
  };
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  loginWithGoogle: (googleToken: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  fetchUser: () => Promise<void>;
}

const API_BASE_URL = 'http://localhost:3001/api';

export const useAuthStore = create<AuthState>()(persist(
  (set, get) => ({
    user: null,
    token: null,
    isLoading: false,
    error: null,

    login: async (email: string, password: string) => {
      set({ isLoading: true, error: null });
      
      try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        set({
          user: data.data.user,
          token: data.data.token,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Login failed',
        });
        throw error;
      }
    },

    register: async (email: string, password: string, name: string) => {
      set({ isLoading: true, error: null });
      
      try {
        const response = await fetch(`${API_BASE_URL}/auth/register`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password, name }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Registration failed');
        }

        set({
          user: data.data.user,
          token: data.data.token,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Registration failed',
        });
        throw error;
      }
    },

    loginWithGoogle: async (googleToken: string) => {
      set({ isLoading: true, error: null });
      
      try {
        const response = await fetch(`${API_BASE_URL}/auth/google`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token: googleToken }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Google login failed');
        }

        set({
          user: data.data.user,
          token: data.data.token,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Google login failed',
        });
        throw error;
      }
    },

    logout: () => {
      set({
        user: null,
        token: null,
        error: null,
      });
    },

    clearError: () => {
      set({ error: null });
    },

    fetchUser: async () => {
      const { token } = get();
      if (!token) return;

      set({ isLoading: true });
      
      try {
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch user');
        }

        set({
          user: data.data,
          isLoading: false,
        });
      } catch (error) {
        set({
          isLoading: false,
          error: error instanceof Error ? error.message : 'Failed to fetch user',
        });
        // If token is invalid, logout
        get().logout();
      }
    },
  }),
  {
    name: 'auth-storage',
    partialize: (state) => ({ 
      user: state.user, 
      token: state.token 
    }),
  }
));