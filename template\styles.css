:root{
  --bg:#f3f4f6;
  --bg-soft:#f7f7fb;
  --text:#0f172a;
  --muted:#6b7280;
  --accent:#7c3aed;
  --accent2:#ec4899;
  --ring: rgba(124,58,237,.25);
  --shadow: 0 10px 25px rgba(0,0,0,.06), 0 2px 6px rgba(0,0,0,.04);
}
*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji","Segoe UI Emoji";
  color:var(--text);
  background: radial-gradient(1200px 600px at 50% 10%, #ffffff 0%, #f6f7fb 55%, #f2f3f7 100%);
}
a{color:inherit;text-decoration:none}
.container{
  max-width: 1000px;
  margin:0 auto;
  padding:0 24px;
}

/* Header */
.site-header{
  position:sticky; top:0; z-index:50;
  background: rgba(255,255,255,.75);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #ececf2;
}
.nav{
  height:72px;
  display:flex; align-items:center; gap:24px;
}
.brand{display:flex; align-items:center; gap:10px; font-weight:800; font-size:22px;}
.brand-text{letter-spacing:.2px}
.links{margin-left:auto; display:flex; gap:26px; color:#4b5563; font-weight:500}
.links a{padding:8px 2px; border-radius:8px}
.links a:hover{color:#111827; background:#f1f5f9}

.cta{
  font-weight:700;
  border-radius:28px;
  padding:12px 18px;
  box-shadow: var(--shadow);
  transition:.2s ease;
  white-space:nowrap;
}
.cta.ghost{
  border:1px solid #e5e7eb;
  background:#ffffff;
}
.cta.ghost:hover{transform:translateY(-1px)}

/* Hero */
.hero{
  text-align:center;
  padding:68px 16px 28px;
  position:relative;
}
.hero-title{
  font-size: clamp(34px, 6.2vw, 64px);
  line-height:1.05;
  letter-spacing:-.02em;
  margin:24px auto 12px;
  font-weight:900;
}
.gradient-text{
  background: linear-gradient(90deg, var(--accent) 0%, #fdba74 40%, var(--accent2) 95%);
  -webkit-background-clip:text;
  background-clip:text;
  color:transparent;
}
.underline{
  position:relative;
}
.underline:after{
  content:"";
  position:absolute; left:0; right:0; bottom:-6px;
  height:6px; border-radius:6px;
  background: linear-gradient(90deg, #fca5a5, #fdba74);
  filter: blur(.2px);
}

.hero-sub{
  max-width:740px;
  margin:18px auto 26px;
  color:#6b7280;
  font-size:clamp(16px, 2.2vw, 18px);
  line-height:1.7;
}
.hero-cta{margin:26px 0 16px}
.cta.solid{
  display:inline-flex; align-items:center; gap:10px;
  padding:16px 22px;
  color:#fff;
  background: linear-gradient(90deg, #6d28d9, #ea4aaa);
  box-shadow: 0 12px 30px rgba(234,74,170,.35), 0 6px 16px rgba(109,40,217,.25);
}
.cta.solid:hover{transform:translateY(-2px)}
.cam-dot{
  width:28px;height:28px;display:inline-grid;place-items:center;
  background:rgba(255,255,255,.15); border-radius:999px; font-size:16px;
}

.mini-note{
  color:#9ca3af;
  font-size:12px;
  margin-top:10px;
}

/* Stickers */
.sticker{
  position: absolute;
  z-index: 1;
  background:#fff;
  border-radius:14px;
  box-shadow: var(--shadow);
  border:1px solid #ececf2;
  padding:10px;
}
.sticker-wand{left:210px; top:140px; transform:rotate(-12deg)}
.sticker-clip{left:290px; top:370px; transform:rotate(-15deg)}
.sticker-avatar{right:160px; top:170px; transform:rotate(12deg)}
.sticker-emoji{font-size:22px; display:block}
.avatar{
  width:38px;height:38px;border-radius:10px;overflow:hidden;border:4px solid #fff;
  box-shadow: var(--shadow);
}
.avatar img{width:100%;height:100%;object-fit:cover}

/* Steps */
.steps{
  background: linear-gradient(180deg, transparent, #ffffff 18%, #ffffff);
  padding:64px 0 90px;
  margin-top:40px;
}
.steps-title{
  text-align:center;
  font-size: clamp(26px, 4.2vw, 44px);
  line-height:1.15;
  letter-spacing:-.02em;
  margin:0 0 34px;
  font-weight:800;
}
.brand-accent{
  color:#6d28d9;
  text-shadow: 0 8px 40px rgba(109,40,217,.25);
}
.steps-grid{
  display:grid;
  grid-template-columns: repeat(3,minmax(0,1fr));
  gap:28px;
  background: #fafafa;
  border:1px solid #f0f0f4;
  border-radius:22px;
  padding:26px 18px;
  max-width:1040px;
  margin: 0 auto;
  box-shadow: var(--shadow);
}
.step{
  text-align:center;
  padding:18px 8px;
}
.step h3{
  margin:10px 0 6px;
  font-size:20px;
  font-weight:800;
}
.step p{
  margin:0;
  color:#6b7280;
  font-weight:500;
}

/* Responsive */
@media (max-width: 900px){
  .links{display:none}
  .sticker{display:none}
.steps-grid{grid-template-columns:1fr; padding:22px}
}

/* Sections below the fold */
.section-title{
  text-align:center;
  font-size: clamp(26px, 4.2vw, 42px);
  line-height:1.15;
  letter-spacing:-.02em;
  font-weight:800;
  margin: 10px 0 8px;
}
.section-title.alt{
  color:#a855f7;
}
.section-sub{
  text-align:center;
  color:#6b7280;
  max-width:820px;
  margin: 8px auto 26px;
}

/* Examples */
.examples{
  padding:64px 0 20px;
}
.example-media{
  margin:28px auto 0;
  max-width:840px;
  border-radius:18px;
  overflow:hidden;
  box-shadow: var(--shadow);
  border: 1px solid #ececf2;
}
.example-media img{display:block; width:100%; height:auto}

/* Testimonials */
.testimonials{
  background:#f6f7fb;
  padding:70px 0;
  border-top:1px solid #eee;
  border-bottom:1px solid #eee;
}
.cards{
  display:grid;
  grid-template-columns: repeat(3,minmax(0,1fr));
  gap:22px;
  margin-top:22px;
}
.card{
  background:#fff;
  border:1px solid #ececf2;
  border-radius:18px;
  padding:22px;
  box-shadow: var(--shadow);
  display:flex; flex-direction:column; gap:14px;
}
.card .stars{color:#f59e0b; letter-spacing:2px; font-weight:800}
.person{display:flex; align-items:center; gap:10px; margin-top:auto}
.person img{width:40px; height:40px; border-radius:999px}
.person div{display:flex; flex-direction:column; font-size:13px; color:#6b7280}
.person strong{color:#111827; font-size:14px}

/* FAQ (accordion) */
.faq{
  padding:70px 0;
  background: #f7f7fb;
}
.accordion{
  max-width:980px; margin:18px auto 0; display:flex; flex-direction:column; gap:14px;
}
details{
  background: linear-gradient(180deg, #fff, #fbfbff);
  border:1px solid #ececf2;
  border-radius:16px;
  padding:14px 16px;
  box-shadow: var(--shadow);
}
summary{
  list-style:none; cursor:pointer; position:relative; font-weight:700; color:#1f2937;
}
summary::-webkit-details-marker{display:none}
summary .num{
  display:inline-grid; place-items:center;
  width:28px; height:28px; margin-right:10px;
  border-radius:10px;
  background:radial-gradient(120px 80px at 0% 0%, #f1e8ff, #ffe3f1);
  color:#6d28d9; font-weight:800;
}
details p{
  margin:10px 2px 2px; color:#6b7280;
}

/* CTA Banner */
.cta-banner{
  padding:60px 0 30px;
}
.banner{
  display:flex; align-items:center; justify-content:space-between; gap:20px;
  border-radius:26px;
  padding:30px 30px;
  background: linear-gradient(90deg, #6d28d9, #ea4aaa);
  color:#fff;
  box-shadow: 0 20px 50px rgba(109,40,217,.25);
}
.banner h3{font-size:28px; margin:0 0 6px}
.banner p{margin:0; color:#f3e8ff}
.banner-btn{
  background:#fff;
  color:#6d28d9;
  padding:14px 20px;
  border-radius:28px;
  box-shadow: var(--shadow);
  font-weight:800;
}

/* Footer */
.site-footer{
  padding:40px 0 60px;
  background:#fff;
}
.foot{display:flex; flex-direction:column; align-items:center; gap:14px}
.foot-brand .brand{font-size:20px}
.foot-tag{color:#6b7280; margin:4px 0}
.stats{display:flex; gap:24px; color:#6d28d9}
.stats b{margin-right:6px}
.legal{margin-top:10px; color:#9ca3af; font-size:13px}
.legal a{color:#6d28d9}

@media (max-width: 900px){
  .cards{grid-template-columns:1fr}
  .banner{flex-direction:column; text-align:center}
}
