import { Link } from "react-router-dom";

export default function Home() {
  return (
    <div>
      {/* Header */}
      <header className="site-header">
        <div className="container nav">
          <Link className="brand" to="/">
            <span className="brand-logo">
              {/* VBA code icon */}
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
              </svg>
            </span>
            <span className="brand-text">GenerateVBA</span>
          </Link>
          <nav className="links">
            <a href="#how">How it Works</a>
            <a href="#features">Features</a>
            <a href="#testimonials">Testimonials</a>
            <a href="#pricing">Pricing</a>
          </nav>
          <Link className="cta ghost" to="/auth/signin">Sign In</Link>
        </div>
      </header>

      <main>
        {/* Decorative floating stickers */}
        <div className="sticker sticker-wand">
          <span className="sticker-emoji">💻</span>
        </div>
        <div className="sticker sticker-clip">
          <span className="sticker-emoji">⚡</span>
        </div>
        <div className="sticker sticker-avatar">
          <span className="sticker-emoji">📊</span>
        </div>

        {/* Hero Section */}
        <section className="hero container">
          <h1 className="hero-title">
            Generate VBA Code<br/>
            with <span className="gradient-text underline">AI Magic</span>
          </h1>
          <p className="hero-sub">
            Transform your Excel automation ideas into working VBA macros instantly. 
            Just describe what you want in plain English, and our AI creates 
            professional, ready-to-use VBA code in seconds.
          </p>

          <div className="hero-cta">
            <Link className="cta solid" to="/auth/signin">
              <span className="cam-dot">🚀</span>
              Start Generating Free
            </Link>
          </div>

          <p className="mini-note">
            5 free generations to get started – No credit card required!
          </p>
        </section>

        {/* How it Works */}
        <section id="how" className="steps">
          <div className="container">
            <h2 className="steps-title">
              Create VBA macros with<br/>
              <span className="brand-accent">three simple steps</span>
            </h2>

            <div className="steps-grid">
              <div className="step">
                <h3>📝 Describe Your Need</h3>
                <p>Tell us what you want your macro to do in plain English.</p>
              </div>
              <div className="step">
                <h3>🤖 AI Generates Code</h3>
                <p>Our AI creates professional VBA code instantly.</p>
              </div>
              <div className="step">
                <h3>📋 Copy & Use</h3>
                <p>Copy the code to Excel and watch your automation work.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Features */}
        <section id="features" className="examples">
          <div className="container">
            <h2 className="section-title">
              Powerful Features for <span className="gradient-text underline">Excel Automation</span>
            </h2>
            <p className="section-sub">
              Everything you need to automate Excel tasks, from simple data manipulation to complex business logic.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="card">
                <h3 className="text-xl font-bold mb-3">🔄 Data Processing</h3>
                <p className="text-gray-600">Loop through ranges, filter data, create pivot tables, and manipulate worksheets with ease.</p>
              </div>
              <div className="card">
                <h3 className="text-xl font-bold mb-3">📊 Report Generation</h3>
                <p className="text-gray-600">Automatically generate charts, format reports, and create professional dashboards.</p>
              </div>
              <div className="card">
                <h3 className="text-xl font-bold mb-3">🔗 System Integration</h3>
                <p className="text-gray-600">Connect Excel to databases, APIs, and other applications for seamless workflows.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="testimonials" id="testimonials">
          <div className="container">
            <h2 className="section-title alt">See what users are saying</h2>
            <p className="section-sub">
              Join thousands of Excel users who have automated their workflows with our VBA generator
            </p>

            <div className="cards">
              <article className="card">
                <div className="stars">★★★★★</div>
                <p>"This tool saved me hours of coding! I described a complex data validation macro and got perfect VBA code in seconds. Game changer for my finance reports!"</p>
                <div className="person">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold">JM</div>
                  <div><strong>Jennifer Martinez</strong><span>Financial Analyst</span></div>
                </div>
              </article>

              <article className="card">
                <div className="stars">★★★★★</div>
                <p>"As someone with no programming background, this AI VBA generator is incredible. I can now automate my Excel tasks without learning to code!"</p>
                <div className="person">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold">DS</div>
                  <div><strong>David Smith</strong><span>Operations Manager</span></div>
                </div>
              </article>

              <article className="card">
                <div className="stars">★★★★★</div>
                <p>"The generated VBA code is clean, well-commented, and actually works! It's like having a VBA expert on my team 24/7."</p>
                <div className="person">
                  <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold">ML</div>
                  <div><strong>Maria Lopez</strong><span>Data Analyst</span></div>
                </div>
              </article>
            </div>
          </div>
        </section>

        {/* Pricing */}
        <section className="faq" id="pricing">
          <div className="container">
            <h2 className="section-title">
              Simple <span className="gradient-text underline">Pricing</span>
            </h2>
            <p className="section-sub">
              Start free and upgrade when you need more. No hidden fees, cancel anytime.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 max-w-4xl mx-auto">
              <div className="card text-center">
                <h3 className="text-2xl font-bold mb-2">Free Plan</h3>
                <div className="text-4xl font-bold text-purple-600 mb-4">$0</div>
                <ul className="text-left space-y-2 mb-6">
                  <li>✅ 5 VBA generations per month</li>
                  <li>✅ Basic code templates</li>
                  <li>✅ Community support</li>
                  <li>✅ Copy & download code</li>
                </ul>
                <Link to="/auth/signin" className="cta ghost w-full block text-center">Get Started Free</Link>
              </div>
              
              <div className="card text-center border-2 border-purple-500">
                <div className="bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-bold mb-4 inline-block">Most Popular</div>
                <h3 className="text-2xl font-bold mb-2">Premium</h3>
                <div className="text-4xl font-bold text-purple-600 mb-4">$19.99<span className="text-lg">/month</span></div>
                <ul className="text-left space-y-2 mb-6">
                  <li>✅ Unlimited VBA generations</li>
                  <li>✅ Advanced code templates</li>
                  <li>✅ Priority support</li>
                  <li>✅ Export to .bas files</li>
                  <li>✅ Code history & favorites</li>
                  <li>✅ Custom code styles</li>
                </ul>
                <Link to="/auth/signin" className="cta solid w-full block text-center">Upgrade to Premium</Link>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Banner */}
        <section className="cta-banner">
          <div className="container banner">
            <div className="banner-copy">
              <h3>Ready to Automate Your Excel Tasks?</h3>
              <p>Join thousands of users who have transformed their Excel workflows with AI-generated VBA code!</p>
            </div>
            <Link className="cta ghost banner-btn" to="/auth/signin">Start Free →</Link>
          </div>
        </section>

        {/* Footer */}
        <footer className="site-footer">
          <div className="container foot">
            <div className="foot-brand">
              <div className="brand">
                <span className="brand-logo">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                    <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                    <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
                  </svg>
                </span>
                <span className="brand-text">GenerateVBA</span>
              </div>
              <p className="foot-tag">AI-powered VBA code generation for Excel automation.</p>
              <div className="stats">
                <span><b>2s</b> Avg. Generation</span>
                <span><b>Clean</b> Code Output</span>
                <span><b>Free</b> to Start</span>
              </div>
            </div>
            <div className="legal">© 2025 GenerateVBA · <a href="#">Terms of Service</a> · <a href="#">Privacy Policy</a></div>
          </div>
        </footer>
      </main>
    </div>
  );
}