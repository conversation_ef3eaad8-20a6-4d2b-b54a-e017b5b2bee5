@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root{
  --bg:#f3f4f6;
  --bg-soft:#f7f7fb;
  --text:#0f172a;
  --muted:#6b7280;
  --accent:#7c3aed;
  --accent2:#ec4899;
  --ring: rgba(124,58,237,.25);
  --shadow: 0 10px 25px rgba(0,0,0,.06), 0 2px 6px rgba(0,0,0,.04);
}

*{box-sizing:border-box}
html,body{height:100%}
body{
  margin:0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji","Segoe UI Emoji";
  color:var(--text);
  background: radial-gradient(1200px 600px at 50% 10%, #ffffff 0%, #f6f7fb 55%, #f2f3f7 100%);
}
a{color:inherit;text-decoration:none}

.container{
  max-width: 1000px;
  margin:0 auto;
  padding:0 24px;
}

/* Header */
.site-header{
  position:sticky; top:0; z-index:50;
  background: rgba(255,255,255,.75);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #ececf2;
}
.nav{
  height:72px;
  display:flex; align-items:center; gap:24px;
}
.brand{display:flex; align-items:center; gap:10px; font-weight:800; font-size:22px;}
.brand-text{letter-spacing:.2px}
.links{margin-left:auto; display:flex; gap:26px; color:#4b5563; font-weight:500}
.links a{padding:8px 2px; border-radius:8px}
.links a:hover{color:#111827; background:#f1f5f9}

.cta{
  font-weight:700;
  border-radius:28px;
  padding:12px 18px;
  box-shadow: var(--shadow);
  transition:.2s ease;
  white-space:nowrap;
}
.cta.ghost{
  border:1px solid #e5e7eb;
  background:#ffffff;
}
.cta.ghost:hover{transform:translateY(-1px)}
.cta.solid{
  display:inline-flex; align-items:center; gap:10px;
  padding:16px 22px;
  color:#fff;
  background: linear-gradient(90deg, #6d28d9, #ea4aaa);
  box-shadow: 0 12px 30px rgba(234,74,170,.35), 0 6px 16px rgba(109,40,217,.25);
}
.cta.solid:hover{transform:translateY(-2px)}

/* Hero */
.hero{
  text-align:center;
  padding:68px 16px 28px;
  position:relative;
}
.hero-title{
  font-size: clamp(34px, 6.2vw, 64px);
  line-height:1.05;
  letter-spacing:-.02em;
  margin:24px auto 12px;
  font-weight:900;
}
.gradient-text{
  background: linear-gradient(90deg, var(--accent) 0%, #fdba74 40%, var(--accent2) 95%);
  -webkit-background-clip:text;
  background-clip:text;
  color:transparent;
}
.underline{
  position:relative;
}
.underline:after{
  content:"";
  position:absolute; left:0; right:0; bottom:-6px;
  height:6px; border-radius:6px;
  background: linear-gradient(90deg, #fca5a5, #fdba74);
  filter: blur(.2px);
}

.hero-sub{
  max-width:740px;
  margin:18px auto 26px;
  color:#6b7280;
  font-size:clamp(16px, 2.2vw, 18px);
  line-height:1.7;
}
.hero-cta{margin:26px 0 16px}

.mini-note{
  color:#9ca3af;
  font-size:12px;
  margin-top:10px;
}

/* Stickers */
.sticker{
  position: absolute;
  z-index: 1;
  background:#fff;
  border-radius:14px;
  box-shadow: var(--shadow);
  border:1px solid #ececf2;
  padding:10px;
}
.sticker-wand{left:210px; top:140px; transform:rotate(-12deg)}
.sticker-clip{left:290px; top:370px; transform:rotate(-15deg)}
.sticker-avatar{right:160px; top:170px; transform:rotate(12deg)}
.sticker-emoji{font-size:22px; display:block}

/* Cards */
.card{
  background:#fff;
  border:1px solid #ececf2;
  border-radius:18px;
  padding:22px;
  box-shadow: var(--shadow);
}

/* Sections */
.section-title{
  text-align:center;
  font-size: clamp(26px, 4.2vw, 42px);
  line-height:1.15;
  letter-spacing:-.02em;
  font-weight:800;
  margin: 10px 0 8px;
}
.section-sub{
  text-align:center;
  color:#6b7280;
  max-width:820px;
  margin: 8px auto 26px;
}

/* Code Display */
.code-output{
  background:#111827;
  border-radius:12px;
  padding:20px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color:#e5e7eb;
  overflow-x:auto;
}

/* Form Elements */
.form-input{
  width:100%;
  padding:12px 16px;
  border:1px solid #e5e7eb;
  border-radius:12px;
  font-size:16px;
  transition:.2s ease;
}
.form-input:focus{
  outline:none;
  border-color:var(--accent);
  box-shadow: 0 0 0 3px var(--ring);
}

.form-textarea{
  width:100%;
  padding:16px;
  border:1px solid #e5e7eb;
  border-radius:12px;
  font-size:16px;
  min-height:120px;
  resize:vertical;
  transition:.2s ease;
}
.form-textarea:focus{
  outline:none;
  border-color:var(--accent);
  box-shadow: 0 0 0 3px var(--ring);
}

/* Line Clamp Utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Responsive */
@media (max-width: 900px){
  .links{display:none}
  .sticker{display:none}
  .container{padding:0 16px}
}