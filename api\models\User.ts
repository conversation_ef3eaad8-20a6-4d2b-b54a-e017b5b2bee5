import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  _id: string;
  email: string;
  name: string;
  image?: string;
  provider: 'google' | 'email';
  providerId?: string;
  password?: string;
  subscription: {
    plan: 'free' | 'pro' | 'enterprise';
    status: 'active' | 'canceled' | 'past_due' | 'incomplete';
    paymentCustomerId?: string;
    paymentSubscriptionId?: string;
    currentPeriodEnd?: Date;
  };
  credits: {
    total: number; // -1 for unlimited
    used: number;
    remaining: number; // -1 for unlimited
  };
  preferences: {
    theme: 'light' | 'dark';
    language: string;
    notifications: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  image: {
    type: String,
    default: null
  },
  provider: {
    type: String,
    enum: ['google', 'email'],
    required: true
  },
  providerId: {
    type: String,
    sparse: true
  },
  password: {
    type: String,
    required: function(this: IUser) {
      return this.provider === 'email';
    }
  },
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'pro', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'canceled', 'past_due', 'incomplete'],
      default: 'active'
    },
    paymentCustomerId: String,
    paymentSubscriptionId: String,
    currentPeriodEnd: Date
  },
  credits: {
    total: {
      type: Number,
      default: 5 // Updated to match new free plan
    },
    used: {
      type: Number,
      default: 0
    },
    remaining: {
      type: Number,
      default: 5 // Updated to match new free plan
    }
  },
  preferences: {
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    language: {
      type: String,
      default: 'en'
    },
    notifications: {
      type: Boolean,
      default: true
    }
  }
}, {
  timestamps: true
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ providerId: 1, provider: 1 });

export default mongoose.model<IUser>('User', UserSchema);