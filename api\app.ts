/**
 * This is a API server
 */

import express, { type Request, type Response, type NextFunction }  from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import Database from './config/database.js';
import userRoutes from './routes/users.js';
import codeRoutes from './routes/code.js';
import billingRoutes from './routes/billing.js';
import adminSetupRoutes from './routes/admin-setup.js';
import registerRoutes from './routes/register.js';
import { authOptions } from './auth/[...nextauth].js';
import NextAuth from 'next-auth';

// for esm mode
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// load env
dotenv.config();

// Initialize database connection
const database = Database.getInstance();
database.connect().catch(console.error);

const app: express.Application = express();

app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * NextAuth.js Routes
 */
const nextAuthHandler = NextAuth(authOptions);
app.use('/api/auth', (req, res, next) => {
  // Check if it's a NextAuth route
  const nextAuthRoutes = ['signin', 'signout', 'callback', 'csrf', 'session', 'providers'];
  const pathSegments = req.path.split('/').filter(Boolean);
  
  if (nextAuthRoutes.some(route => pathSegments.includes(route)) || 
      pathSegments.includes('google') || 
      pathSegments.includes('credentials')) {
    // Handle NextAuth routes
    return nextAuthHandler(req, res);
  }
  
  // Fall back to custom auth routes for backward compatibility
  next();
});

/**
 * API Routes
 */
app.use('/api/auth/register', registerRoutes);
app.use('/api/users', userRoutes);
app.use('/api/code', codeRoutes);
app.use('/api/billing', billingRoutes);
app.use('/api/admin-setup', adminSetupRoutes);

/**
 * health
 */
app.use('/api/health', (req: Request, res: Response, next: NextFunction): void => {
  res.status(200).json({
    success: true,
    message: 'ok'
  });
});

/**
 * error handler middleware
 */
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  res.status(500).json({
    success: false,
    error: 'Server internal error'
  });
});

/**
 * 404 handler
 */
app.use((req: Request, res: Response) => {
  res.status(404).json({
    success: false,
    error: 'API not found'
  });
});

export default app;