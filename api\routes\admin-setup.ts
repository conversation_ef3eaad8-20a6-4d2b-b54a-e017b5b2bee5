import express from 'express';
import bcrypt from 'bcryptjs';
import User from '../models/User.js';

const router = express.Router();

/**
 * POST /api/admin-setup/create-admin
 * Creates an admin user (one-time setup)
 */
router.post('/create-admin', async (req, res) => {
  try {
    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      return res.status(400).json({
        success: false,
        error: 'Admin user already exists'
      });
    }

    // Hash password
    const password = 'admin123'; // Default password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create admin user
    const adminUser = new User({
      email: '<EMAIL>',
      name: 'Admin User',
      provider: 'email',
      password: hashedPassword,
      subscription: {
        plan: 'enterprise',
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
      },
      credits: {
        total: 999999,
        used: 0,
        remaining: 999999
      },
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: true
      }
    });

    await adminUser.save();
    
    res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        plan: 'enterprise',
        credits: 999999
      }
    });
    
  } catch (error) {
    console.error('Error creating admin user:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create admin user'
    });
  }
});

export default router;