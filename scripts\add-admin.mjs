import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from '../api/models/User.ts';

dotenv.config();

async function addAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/vba-generator');
    console.log('Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('Admin user already exists!');
      process.exit(0);
    }

    // Hash password
    const password = 'admin123'; // Default password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Create admin user
    const adminUser = new User({
      email: '<EMAIL>',
      name: 'Admin User',
      provider: 'email',
      password: hashedPassword,
      subscription: {
        plan: 'enterprise',
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
      },
      credits: {
        total: 999999,
        used: 0,
        remaining: 999999
      },
      preferences: {
        theme: 'light',
        language: 'en',
        notifications: true
      }
    });

    await adminUser.save();
    console.log('Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('Plan: Enterprise (Unlimited Credits)');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

addAdminUser();