import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useSession, signOut } from 'next-auth/react';
import { Copy, Save, Zap, AlertCircle, CheckCircle, Home, History, User, LogOut } from 'lucide-react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

interface GenerationResult {
  _id: string;
  generatedCode: string;
  prompt: string;
  language: string;
}

export default function Generate() {
  const [prompt, setPrompt] = useState("");
  const [generatedCode, setGeneratedCode] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [generationId, setGenerationId] = useState<string | null>(null);
  const [userCredits, setUserCredits] = useState<number | null>(null);
  
  const { data: session, status } = useSession();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (status === 'loading') return;
    
    if (!session?.user) {
      navigate('/signin');
      return;
    }
    
    fetchUserCredits();
  }, [session, status, navigate]);
  
  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/users/credits', {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setUserCredits(data.data.remaining);
      }
    } catch (error) {
      console.error('Failed to fetch user credits:', error);
    }
  };
  
  const handleLogout = async () => {
    await signOut({ redirect: false });
    navigate('/');
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) return;
    
    if (userCredits !== null && userCredits <= 0) {
      setError('You have no credits remaining. Please upgrade your plan to continue generating code.');
      return;
    }
    
    setIsGenerating(true);
    setError('');
    setSuccess('');
    setGenerationId(null);
    
    try {
      const response = await fetch('/api/code/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          language: 'vba',
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setGeneratedCode(data.data.code);
        setGenerationId(data.data.id);
        setSuccess('VBA code generated successfully!');
        // Update credits
        if (userCredits !== null) {
          setUserCredits(userCredits - 1);
        }
      } else {
        setError(data.error || 'Failed to generate code. Please try again.');
      }
    } catch (error) {
      console.error('Generation error:', error);
      setError('Network error. Please check your connection and try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generatedCode);
      setSuccess('Code copied to clipboard!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Failed to copy code to clipboard');
      setTimeout(() => setError(''), 3000);
    }
  };
  
  const saveToHistory = async () => {
    if (!generationId) {
      setError('No generation to save');
      return;
    }
    
    setIsSaving(true);
    try {
      // The code is already saved when generated, so we just show success
      setSuccess('Code saved to history!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (error) {
      setError('Failed to save code');
      setTimeout(() => setError(''), 3000);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link to="/" className="brand">
                <span className="brand-logo">
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect x="2" y="2" width="20" height="20" rx="5" fill="#7c3aed"/>
                    <rect x="4.5" y="4.5" width="15" height="15" rx="4" fill="white"/>
                    <path d="M8 9h2v6H8V9zm4 0h2l2 6h-2l-.4-1.2h-1.2L12 15h-2l2-6zm1.2 3.6h.6l-.3-1.2-.3 1.2z" fill="#7c3aed"/>
                  </svg>
                </span>
                <span className="brand-text">GenerateVBA</span>
              </Link>
            </div>
            <nav className="flex items-center space-x-4">
              <Link to="/dashboard" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <Home className="h-4 w-4 mr-1" />
                Dashboard
              </Link>
              <Link to="/history" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <History className="h-4 w-4 mr-1" />
                History
              </Link>
              <Link to="/profile" className="text-gray-700 hover:text-purple-600 px-3 py-2 rounded-md text-sm font-medium flex items-center">
                <User className="h-4 w-4 mr-1" />
                Profile
              </Link>
              <div className="flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200">
                <span className="text-sm text-gray-600">Hi, {session?.user?.name || 'User'}</span>
                <button
                  onClick={handleLogout}
                  className="text-gray-700 hover:text-red-600 p-2 rounded-md transition-colors"
                  title="Logout"
                >
                  <LogOut className="h-4 w-4" />
                </button>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Generate VBA Code with AI
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Describe what you want your Excel macro to do in plain English, and our AI will create professional VBA code for you.
            </p>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-center">
              <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
          )}
          
          {success && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center">
              <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
              <span className="text-sm text-green-600">{success}</span>
            </div>
          )}

          {/* Generation Form */}
          <div className="card mb-8">
            <h2 className="text-xl font-bold mb-4 flex items-center">
              <Zap className="h-5 w-5 text-purple-600 mr-2" />
              Describe Your Macro
            </h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 mb-2">
                  What do you want your VBA macro to do?
                </label>
                <textarea
                  id="prompt"
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Example: Create a macro that loops through column A, finds all cells containing 'error', highlights them in red, and creates a summary count at the bottom..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  💡 Tip: Be specific about what data to process, what actions to take, and any formatting requirements.
                </div>
                <button
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating || (userCredits !== null && userCredits <= 0)}
                  className="cta solid disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isGenerating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating...
                    </>
                  ) : (
                    <>
                      <Zap className="h-5 w-5 mr-2" />
                      Generate VBA Code
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Generated Code */}
          {generatedCode && (
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  Generated VBA Code
                </h2>
                <div className="flex space-x-2">
                  <button
                    onClick={copyToClipboard}
                    className="cta ghost text-sm flex items-center"
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Code
                  </button>
                  <button 
                    onClick={saveToHistory}
                    disabled={isSaving}
                    className="cta ghost text-sm flex items-center disabled:opacity-50"
                  >
                    <Save className="h-4 w-4 mr-1" />
                    {isSaving ? 'Saving...' : 'Saved'}
                  </button>
                </div>
              </div>
              
              <div className="rounded-lg overflow-hidden">
                <SyntaxHighlighter
                  language="vbnet"
                  style={vscDarkPlus}
                  customStyle={{
                    margin: 0,
                    padding: '1rem',
                    fontSize: '0.875rem',
                    lineHeight: '1.5',
                  }}
                  showLineNumbers={true}
                  wrapLines={true}
                >
                  {generatedCode}
                </SyntaxHighlighter>
              </div>
              
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-900 mb-2">How to use this code:</h3>
                <ol className="text-blue-800 text-sm space-y-1">
                  <li>1. Open Excel and press Alt + F11 to open the VBA editor</li>
                  <li>2. Insert a new module (Insert → Module)</li>
                  <li>3. Copy and paste the generated code</li>
                  <li>4. Press F5 to run the macro or close the editor and run from Excel</li>
                </ol>
              </div>
            </div>
          )}

          {/* Usage Stats */}
          <div className="mt-8 text-center">
            <div className={`inline-flex items-center px-4 py-2 rounded-full ${
              userCredits !== null && userCredits <= 0 
                ? 'bg-red-100' 
                : userCredits !== null && userCredits <= 5 
                ? 'bg-yellow-100' 
                : 'bg-purple-100'
            }`}>
              <span className={`text-sm ${
                userCredits !== null && userCredits <= 0 
                  ? 'text-red-800' 
                  : userCredits !== null && userCredits <= 5 
                  ? 'text-yellow-800' 
                  : 'text-purple-800'
              }`}>
                {userCredits !== null && userCredits <= 0 ? '⚠️' : '💎'} You have{' '}
                <strong>
                  {userCredits !== null ? userCredits : '...'} credit{userCredits !== 1 ? 's' : ''}
                </strong>{' '}
                remaining
              </span>
            </div>
            {userCredits !== null && userCredits <= 5 && (
              <div className="mt-2">
                <Link to="/billing" className="text-purple-600 hover:text-purple-500 text-sm font-medium">
                  Upgrade to Premium for unlimited generations →
                </Link>
              </div>
            )}
          </div>

          {/* Examples */}
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-center mb-8">Need inspiration? Try these examples:</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="card cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setPrompt("Create a macro that automatically formats a data table with headers, applies borders, and adds alternating row colors for better readability.")}>
                <h3 className="font-semibold mb-2">📊 Format Data Table</h3>
                <p className="text-gray-600 text-sm">Automatically format tables with headers, borders, and alternating colors.</p>
              </div>
              
              <div className="card cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setPrompt("Write a macro that validates email addresses in column A, highlights invalid ones in red, and creates a summary count of valid vs invalid emails.")}>
                <h3 className="font-semibold mb-2">✅ Email Validation</h3>
                <p className="text-gray-600 text-sm">Validate email addresses and highlight invalid entries.</p>
              </div>
              
              <div className="card cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setPrompt("Create a macro that imports data from a CSV file, removes duplicates, sorts by date, and creates a pivot table summary.")}>
                <h3 className="font-semibold mb-2">📥 Data Import & Clean</h3>
                <p className="text-gray-600 text-sm">Import CSV data, clean duplicates, and create summaries.</p>
              </div>
              
              <div className="card cursor-pointer hover:shadow-lg transition-shadow" onClick={() => setPrompt("Write a macro that generates a monthly report with charts, calculates totals and averages, and saves it as a PDF.")}>
                <h3 className="font-semibold mb-2">📈 Monthly Report</h3>
                <p className="text-gray-600 text-sm">Generate automated reports with charts and export to PDF.</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}