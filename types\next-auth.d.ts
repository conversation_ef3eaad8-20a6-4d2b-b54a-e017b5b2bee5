import NextAuth, { DefaultSession } from 'next-auth'
import { JWT } from 'next-auth/jwt'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      provider: 'google' | 'email'
      subscription: {
        plan: 'free' | 'pro' | 'enterprise'
        status: 'active' | 'canceled' | 'past_due'
        stripeCustomerId?: string
        stripeSubscriptionId?: string
        currentPeriodEnd?: Date
      }
      credits: {
        total: number
        used: number
        remaining: number
      }
      preferences: {
        theme: 'light' | 'dark'
        language: string
        notifications: boolean
      }
    } & DefaultSession['user']
  }

  interface User {
    id: string
    provider: 'google' | 'email'
    subscription: {
      plan: 'free' | 'pro' | 'enterprise'
      status: 'active' | 'canceled' | 'past_due'
      stripeCustomerId?: string
      stripeSubscriptionId?: string
      currentPeriodEnd?: Date
    }
    credits: {
      total: number
      used: number
      remaining: number
    }
    preferences: {
      theme: 'light' | 'dark'
      language: string
      notifications: boolean
    }
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string
    provider: 'google' | 'email'
    subscription: {
      plan: 'free' | 'pro' | 'enterprise'
      status: 'active' | 'canceled' | 'past_due'
      stripeCustomerId?: string
      stripeSubscriptionId?: string
      currentPeriodEnd?: Date
    }
    credits: {
      total: number
      used: number
      remaining: number
    }
    preferences: {
      theme: 'light' | 'dark'
      language: string
      notifications: boolean
    }
  }
}