import { PaymentProvider, PaymentCustomer, PaymentSubscription, CheckoutSession, WebhookEvent, PaymentPlan } from '../types.js';
import { paymentConfig } from '../config.js';
import crypto from 'crypto';

export class DodoPaymentsProvider implements PaymentProvider {
  private apiKey: string;
  private secretKey: string;
  private webhookSecret: string;
  private proPlanId: string;
  private environment: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.DODOPAYMENTS_API_KEY || '';
    this.secretKey = process.env.DODOPAYMENTS_SECRET_KEY || '';
    this.webhookSecret = process.env.DODOPAYMENTS_WEBHOOK_SECRET || '';
    this.proPlanId = process.env.DODOPAYMENTS_PRO_PLAN_ID || '';
    this.environment = process.env.DODOPAYMENTS_ENVIRONMENT || 'sandbox';

    if (!this.apiKey) {
      throw new Error('DODOPAYMENTS_API_KEY is required');
    }

    if (!this.secretKey) {
      throw new Error('DODOPAYMENTS_SECRET_KEY is required');
    }

    if (!this.webhookSecret) {
      throw new Error('DODOPAYMENTS_WEBHOOK_SECRET is required');
    }

    if (!this.proPlanId) {
      throw new Error('DODOPAYMENTS_PRO_PLAN_ID is required');
    }

    this.baseUrl = this.environment === 'production' 
      ? 'https://api.dodopayments.com/v1'
      : 'https://sandbox-api.dodopayments.com/v1';
  }

  async createCustomer(data: {
    email: string;
    name: string;
    metadata?: Record<string, string>;
  }): Promise<PaymentCustomer> {
    const response = await this.makeRequest('POST', '/customers', {
      email: data.email,
      name: data.name,
      metadata: data.metadata
    });

    return {
      id: response.id,
      email: response.email,
      name: response.name,
      metadata: response.metadata
    };
  }

  async getCustomer(customerId: string): Promise<PaymentCustomer> {
    const response = await this.makeRequest('GET', `/customers/${customerId}`);

    return {
      id: response.id,
      email: response.email,
      name: response.name,
      metadata: response.metadata
    };
  }

  async createCheckoutSession(data: {
    customerId: string;
    planId: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<CheckoutSession> {
    if (data.planId !== 'pro') {
      throw new Error('Only pro plan is available for checkout');
    }

    const response = await this.makeRequest('POST', '/checkout/sessions', {
      customer_id: data.customerId,
      plan_id: this.proPlanId,
      success_url: data.successUrl,
      cancel_url: data.cancelUrl,
      metadata: {
        ...data.metadata,
        planId: data.planId
      }
    });

    return {
      id: response.id,
      url: response.checkout_url,
      customerId: data.customerId,
      planId: data.planId,
      metadata: response.metadata
    };
  }

  async getSubscription(subscriptionId: string): Promise<PaymentSubscription> {
    const response = await this.makeRequest('GET', `/subscriptions/${subscriptionId}`);

    return {
      id: response.id,
      customerId: response.customer_id,
      planId: 'pro',
      status: this.mapDodoStatus(response.status),
      currentPeriodStart: new Date(response.current_period_start),
      currentPeriodEnd: new Date(response.current_period_end),
      cancelAtPeriodEnd: response.cancel_at_period_end
    };
  }

  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd: boolean = true): Promise<PaymentSubscription> {
    const response = await this.makeRequest('PATCH', `/subscriptions/${subscriptionId}`, {
      cancel_at_period_end: cancelAtPeriodEnd
    });

    return {
      id: response.id,
      customerId: response.customer_id,
      planId: 'pro',
      status: this.mapDodoStatus(response.status),
      currentPeriodStart: new Date(response.current_period_start),
      currentPeriodEnd: new Date(response.current_period_end),
      cancelAtPeriodEnd: response.cancel_at_period_end
    };
  }

  async constructWebhookEvent(payload: string | Buffer, signature: string, secret?: string): Promise<WebhookEvent> {
    const webhookSecret = secret || this.webhookSecret;
    const payloadString = typeof payload === 'string' ? payload : payload.toString();
    
    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', webhookSecret)
      .update(payloadString)
      .digest('hex');

    if (signature !== `sha256=${expectedSignature}`) {
      throw new Error('Invalid webhook signature');
    }

    const event = JSON.parse(payloadString);

    return {
      id: event.id,
      type: event.type,
      data: event.data,
      created: new Date(event.created_at)
    };
  }

  async getPlans(): Promise<PaymentPlan[]> {
    return paymentConfig.getPlans();
  }

  private async makeRequest(method: string, endpoint: string, data?: any): Promise<any> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      'X-API-Key': this.secretKey
    };

    const options: RequestInit = {
      method,
      headers
    };

    if (data && (method === 'POST' || method === 'PATCH' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        throw new Error(`DodoPayments API Error: ${response.status} - ${errorData.message}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to make request to DodoPayments API');
    }
  }

  private mapDodoStatus(status: string): 'active' | 'canceled' | 'past_due' | 'incomplete' {
    switch (status.toLowerCase()) {
      case 'active':
        return 'active';
      case 'cancelled':
      case 'canceled':
        return 'canceled';
      case 'past_due':
      case 'overdue':
        return 'past_due';
      case 'incomplete':
      case 'pending':
      case 'failed':
        return 'incomplete';
      default:
        return 'incomplete';
    }
  }
}